import React, { useState, useEffect } from 'react'
import { Settings as SettingsIcon } from 'lucide-react'
import { useAuthStore } from './store/authStore'
import { useConfigStore } from './store/configStore'
import { useUserStore } from './store/userStore'
import FileExplorer from './components/FileExplorer'
import FileOperations from './components/FileOperations'
import Settings from './components/Settings'

const App: React.FC = () => {
  const { isAuthenticated, user, login, logout, autoLogin } = useAuthStore()
  const { loadConfig } = useConfigStore()
  const { profile, loadUserData, loadUserPhoto, clearUserData } = useUserStore()
  const [isLoading, setIsLoading] = useState(false)
  const [isAutoLoading, setIsAutoLoading] = useState(true)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [settingsDefaultTab, setSettingsDefaultTab] = useState<'general' | 'sync' | 'account' | 'about'>('general')

  // 初始化配置和尝试自动登录
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('🚀 Initializing application...')
        await loadConfig()
        console.log('✅ Configuration loaded')

        // 尝试自动登录
        console.log('🔄 Attempting auto-login...')
        const autoLoginSuccess = await autoLogin()
        if (autoLoginSuccess) {
          console.log('✅ Auto login successful')
          // 自动登录成功后，确保用户数据也被加载
          await loadUserData()
          await loadUserPhoto()
          console.log('✅ User data and photo loaded')
        } else {
          console.log('❌ Auto login failed or not enabled')
          // 多账号同步功能已删除
        }
      } catch (error) {
        console.error('❌ Application initialization failed:', error)
      } finally {
        setIsAutoLoading(false)
        console.log('🏁 Application initialization completed')
      }
    }

    initializeApp()
  }, [loadConfig, autoLogin, loadUserData, loadUserPhoto])

  // 当用户登录后加载用户数据
  useEffect(() => {
    if (isAuthenticated) {
      loadUserData().then(() => {
        // 用户数据加载完成后，加载头像
        loadUserPhoto()
      })
    } else {
      clearUserData()
    }
  }, [isAuthenticated, loadUserData, loadUserPhoto, clearUserData])

  const handleLogin = async () => {
    setIsLoading(true)
    setMessage(null)
    try {
      await login()
      setMessage({ type: 'success', text: '登录成功！' })
    } catch (error) {
      console.error('Login failed:', error)
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : '登录失败，请重试' 
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = async () => {
    try {
      await logout()
      setMessage({ type: 'success', text: '已退出登录' })
    } catch (error) {
      console.error('Logout failed:', error)
      setMessage({ type: 'error', text: '退出登录失败' })
    }
  }

  // 自动清除消息
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [message])

  if (!isAuthenticated) {
    // 如果正在自动登录，显示加载状态
    if (isAutoLoading) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          backgroundColor: '#f5f5f7',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
        }}>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '20px'
          }}>
            <div className="modern-loading" style={{ scale: '1.5' }}></div>
            <p style={{
              fontSize: '16px',
              color: '#1d1d1f',
              margin: 0,
              textAlign: 'center',
              lineHeight: '1.5'
            }}>
              正在检查登录状态...
            </p>

            <p style={{
              fontSize: '14px',
              color: '#86868b',
              margin: 0,
              textAlign: 'center',
              lineHeight: '1.4'
            }}>
              请稍候
            </p>
          </div>
        </div>
      )
    }

    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        backgroundColor: '#f5f5f7',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '40px',
          maxWidth: '400px',
          padding: '40px',
          backgroundColor: 'white',
          borderRadius: '16px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ textAlign: 'center' }}>
            <h1 style={{
              fontSize: '32px',
              fontWeight: '700',
              color: '#1d1d1f',
              margin: '0 0 16px 0'
            }}>
              OD-Commander
            </h1>
            <p style={{
              fontSize: '16px',
              color: '#6d6d70',
              margin: 0,
              lineHeight: '1.5'
            }}>
              连接到您的 OneDrive 账户以开始管理文件
            </p>
          </div>

          {isLoading && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              fontSize: '14px',
              color: '#007aff'
            }}>
              <span className="modern-loading"></span>
              加载中
            </div>
          )}

          <button
            onClick={handleLogin}
            disabled={isLoading}
            className="modern-button primary"
            style={{
              width: '100%',
              fontSize: '16px',
              fontWeight: '600'
            }}
          >
            {isLoading ? (
              <>
                <span className="modern-loading"></span>
                连接中...
              </>
            ) : (
              '连接 OneDrive'
            )}
          </button>

          <p style={{
            fontSize: '12px',
            color: '#86868b',
            margin: 0,
            textAlign: 'center',
            lineHeight: '1.4'
          }}>
            我们使用 Microsoft Graph API 安全连接到您的 OneDrive
          </p>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      background: '#ffffff'
    }}>
      {/* 简约头部 */}
      <header className="modern-header" style={{
        padding: '12px 20px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        minHeight: '56px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <img 
            src="/assets/icon.svg" 
            alt="OD-Commander Logo" 
            style={{ 
              width: '32px', 
              height: '32px'
            }} 
          />
          <h1 style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#1d1d1f'
          }}>
            OD Commander
          </h1>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {user && (
            <button
              onClick={() => {
                setShowSettings(true)
                setSettingsDefaultTab('account')
              }}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '6px 12px',
                background: '#f9f9f9',
                borderRadius: '8px',
                border: 'none',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#e8e8e8'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#f9f9f9'
              }}
              title="查看账户信息"
            >
              <div style={{
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                overflow: 'hidden',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: profile?.photo ? 'transparent' : '#007aff',
                color: 'white',
                fontSize: '12px',
                fontWeight: '600'
              }}>
                {profile?.photo ? (
                  <img
                    src={profile.photo}
                    alt="用户头像"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                    onError={(e) => {
                      // 如果头像加载失败，显示字母头像
                      e.currentTarget.style.display = 'none'
                      e.currentTarget.parentElement!.style.background = '#007aff'
                      e.currentTarget.parentElement!.innerHTML = (profile?.displayName || user?.displayName)?.charAt(0) || 'U'
                    }}
                  />
                ) : (
                  (profile?.displayName || user?.displayName)?.charAt(0) || 'U'
                )}
              </div>
              <span style={{
                fontSize: '14px',
                color: '#1d1d1f',
                fontWeight: '500'
              }}>
                {profile?.displayName || user?.displayName || user?.userPrincipalName}
              </span>
            </button>
          )}
          
          <button
            onClick={() => {
              setShowSettings(true)
              setSettingsDefaultTab('general')
            }}
            style={{
              padding: '8px',
              border: 'none',
              background: 'transparent',
              borderRadius: '6px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#6d6d70',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f0f0f0'
              e.currentTarget.style.color = '#1d1d1f'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
              e.currentTarget.style.color = '#6d6d70'
            }}
            title="设置"
          >
            <SettingsIcon size={18} />
          </button>
          
          <button
            onClick={handleLogout}
            className="modern-button secondary"
            style={{ fontSize: '14px' }}
          >
            退出登录
          </button>
        </div>
      </header>

      {/* 消息提示 */}
      {message && (
        <div style={{
          padding: '12px 20px',
          borderBottom: '1px solid #e5e5e7'
        }}>
          <div style={{
            padding: '12px 16px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500'
          }} className={message.type === 'success' ? 'message-success' : 'message-error'}>
            {message.text}
          </div>
        </div>
      )}

      {/* 主内容区域 */}
      <div style={{
        flex: 1,
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* 文件操作侧边栏 */}
        <div style={{ width: '280px', flexShrink: 0 }}>
          <FileOperations />
        </div>

        {/* 文件浏览器主区域 */}
        <div style={{
          flex: 1,
          padding: '16px',
          background: '#fafafa',
          minWidth: 0
        }}>
          <FileExplorer />
        </div>
      </div>

      {/* 设置弹窗 */}
      <Settings
        isVisible={showSettings}
        onClose={() => setShowSettings(false)}
        defaultTab={settingsDefaultTab}
      />
    </div>
  )
}

export default App 