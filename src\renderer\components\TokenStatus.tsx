import React, { useState, useEffect } from 'react'
import { RefreshC<PERSON>, Clock, AlertTriangle, CheckCircle } from 'lucide-react'

interface TokenStatusProps {
  className?: string
}

interface TokenInfo {
  remainingMinutes: number
  isExpired: boolean
  canRefresh: boolean
}

export const TokenStatus: React.FC<TokenStatusProps> = ({ className = '' }) => {
  const [tokenInfo, setTokenInfo] = useState<TokenInfo>({
    remainingMinutes: 0,
    isExpired: true,
    canRefresh: false
  })
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null)

  // 获取token状态
  const fetchTokenStatus = async () => {
    try {
      const response = await window.electronAPI.auth.getTokenStatus()
      setTokenInfo({
        remainingMinutes: response.remainingMinutes || 0,
        isExpired: response.remainingMinutes <= 0,
        canRefresh: response.hasRefreshToken || false
      })
    } catch (error) {
      console.error('Failed to fetch token status:', error)
      setTokenInfo({
        remainingMinutes: 0,
        isExpired: true,
        canRefresh: false
      })
    }
  }

  // 刷新token
  const handleRefreshToken = async () => {
    if (isRefreshing) return

    setIsRefreshing(true)
    try {
      const response = await window.electronAPI.auth.refreshToken()
      if (response.success) {
        setLastRefresh(new Date())
        await fetchTokenStatus()
        console.log('Token refreshed successfully')
      } else {
        throw new Error(response.error || 'Refresh failed')
      }
    } catch (error) {
      console.error('Failed to refresh token:', error)
      // 可以显示错误提示
    } finally {
      setIsRefreshing(false)
    }
  }

  // 定期更新token状态
  useEffect(() => {
    fetchTokenStatus()
    
    // 每分钟更新一次
    const interval = setInterval(fetchTokenStatus, 60000)
    
    return () => clearInterval(interval)
  }, [])

  // 获取状态颜色和图标
  const getStatusDisplay = () => {
    if (tokenInfo.isExpired) {
      return {
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        icon: AlertTriangle,
        status: '已过期',
        description: '需要重新登录'
      }
    }
    
    if (tokenInfo.remainingMinutes <= 10) {
      return {
        color: 'text-orange-500',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200',
        icon: AlertTriangle,
        status: '即将过期',
        description: `${tokenInfo.remainingMinutes}分钟后过期`
      }
    }
    
    if (tokenInfo.remainingMinutes <= 60) {
      return {
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200',
        icon: Clock,
        status: '1小时内过期',
        description: `${tokenInfo.remainingMinutes}分钟后过期`
      }
    }
    
    return {
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      icon: CheckCircle,
      status: '正常',
      description: formatRemainingTime(tokenInfo.remainingMinutes)
    }
  }

  // 格式化剩余时间显示
  const formatRemainingTime = (minutes: number): string => {
    if (minutes <= 0) return '已过期'
    if (minutes < 60) return `${minutes}分钟`
    
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    
    if (hours < 24) {
      return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
    }
    
    const days = Math.floor(hours / 24)
    const remainingHours = hours % 24
    
    return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`
  }

  const statusDisplay = getStatusDisplay()
  const StatusIcon = statusDisplay.icon

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* 状态指示器 */}
      <div className={`flex items-center space-x-2 px-3 py-1.5 rounded-lg border ${statusDisplay.bgColor} ${statusDisplay.borderColor}`}>
        <StatusIcon className={`w-4 h-4 ${statusDisplay.color}`} />
        <div className="flex flex-col">
          <span className={`text-xs font-medium ${statusDisplay.color}`}>
            {statusDisplay.status}
          </span>
          <span className={`text-xs text-gray-500`}>
            {statusDisplay.description}
          </span>
        </div>
      </div>

      {/* 刷新按钮 */}
      {tokenInfo.canRefresh && (
        <button
          onClick={handleRefreshToken}
          disabled={isRefreshing}
          className={`
            flex items-center justify-center w-8 h-8 rounded-lg border
            transition-colors duration-200
            ${isRefreshing 
              ? 'bg-gray-100 border-gray-200 cursor-not-allowed' 
              : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
            }
          `}
          title="手动刷新token"
        >
          <RefreshCw 
            className={`w-4 h-4 text-gray-600 ${isRefreshing ? 'animate-spin' : ''}`} 
          />
        </button>
      )}

      {/* 最后刷新时间提示 */}
      {lastRefresh && (
        <span className="text-xs text-gray-400">
          {lastRefresh.toLocaleTimeString()} 刷新
        </span>
      )}
    </div>
  )
}

export default TokenStatus 