{"name": "od-commander", "version": "1.0.0", "description": "A modern OneDrive third-party client for Windows", "main": "./out/main/index.js", "author": "OD-Commander Team", "homepage": "https://github.com/od-commander/od-commander", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "package": "electron-packager . od-commander --platform=win32 --arch=x64 --out=dist/ --overwrite"}, "keywords": ["onedrive", "electron", "file-manager", "sync", "cloud-storage"], "license": "MIT", "devDependencies": {"@electron/packager": "^18.3.6", "@vitejs/plugin-react": "^4.5.2", "electron": "^36.4.0", "electron-vite": "^3.1.0", "vite": "^6.3.5"}, "dependencies": {"@azure/msal-node": "^3.6.0", "@azure/msal-node-extensions": "^1.5.14", "@fluentui/react-components": "^9.56.7", "@fluentui/react-icons": "^2.0.267", "@microsoft/microsoft-graph-client": "^3.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "get-port": "^7.1.0", "lucide-react": "^0.514.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "zustand": "^5.0.2"}}