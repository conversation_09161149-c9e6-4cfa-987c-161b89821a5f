import React, { useState, useEffect } from 'react'
import {
  Settings as SettingsIcon,
  Monitor,
  Upload,
  Folder,
  Info,
  LogOut,
  Save,
  RotateCcw,
  FolderOpen,
  FileDown,
  FileUp,
  Trash2,
  RefreshCw,
  User
} from 'lucide-react'
import { useConfigStore, AppConfig } from '../store/configStore'
import { useAuthStore } from '../store/authStore'
import { useUserStore, formatBytes, getStoragePercentage } from '../store/userStore'

interface SettingsProps {
  isVisible: boolean
  onClose: () => void
  defaultTab?: 'general' | 'sync' | 'account' | 'about'
}

const Settings: React.FC<SettingsProps> = ({ isVisible, onClose, defaultTab = 'general' }) => {
  const [activeTab, setActiveTab] = useState<'general' | 'sync' | 'account' | 'about'>(defaultTab)
  const [configPath, setConfigPath] = useState<string>('')
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  
  const { 
    config, 
    isLoading, 
    error, 
    loadConfig, 
    updateConfig, 
    resetConfig, 
    getConfigPath,
    exportConfig,
    importConfig,
    clearCache
  } = useConfigStore()
  
  const { user, logout, login } = useAuthStore()
  const {
    profile,
    storageInfo,
    isLoading: userLoading,
    loadUserData,
    loadUserPhoto,
    clearUserData
  } = useUserStore()
  // 多账号管理功能已删除

  // 当defaultTab变化时更新activeTab
  useEffect(() => {
    if (isVisible) {
      setActiveTab(defaultTab)
    }
  }, [isVisible, defaultTab])

  // 加载配置和配置路径
  useEffect(() => {
    if (isVisible) {
      loadConfig()
      loadUserData() // 加载用户数据
      getConfigPath().then(setConfigPath).catch(console.error)
    }
  }, [isVisible, loadConfig, loadUserData, getConfigPath])

  // 显示消息
  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text })
    setTimeout(() => setMessage(null), 3000)
  }

  const handleSettingChange = async (key: string, value: any) => {
    try {
      await updateConfig({ [key]: value })
      showMessage('success', '设置已保存')
    } catch (error) {
      showMessage('error', '保存设置失败')
    }
  }

  const handleSave = () => {
    showMessage('success', '所有设置已保存')
    onClose()
  }

  const handleReset = async () => {
    if (confirm('确定要重置所有设置为默认值吗？')) {
      try {
        await resetConfig()
        showMessage('success', '设置已重置为默认值')
      } catch (error) {
        showMessage('error', '重置设置失败')
      }
    }
  }

  const handleSelectDownloadPath = async () => {
    try {
      const result = await window.electronAPI.showOpenDialog({
        properties: ['openDirectory'],
        title: '选择下载文件夹'
      })
      
      if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
        await handleSettingChange('downloadPath', result.filePaths[0])
      }
    } catch (error) {
      showMessage('error', '选择文件夹失败')
    }
  }

  const handleExportConfig = async () => {
    try {
      const result = await window.electronAPI.showSaveDialog({
        title: '导出配置',
        defaultPath: 'od-commander-config.json',
        filters: [
          { name: 'JSON文件', extensions: ['json'] }
        ]
      })
      
      if (!result.canceled && result.filePath) {
        await exportConfig(result.filePath)
        showMessage('success', '配置已导出')
      }
    } catch (error) {
      showMessage('error', '导出配置失败')
    }
  }

  const handleImportConfig = async () => {
    try {
      const result = await window.electronAPI.showOpenDialog({
        title: '导入配置',
        filters: [
          { name: 'JSON文件', extensions: ['json'] }
        ],
        properties: ['openFile']
      })
      
      if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
        await importConfig(result.filePaths[0])
        showMessage('success', '配置已导入')
      }
    } catch (error) {
      showMessage('error', '导入配置失败')
    }
  }

  const handleClearCache = async () => {
    if (confirm('确定要清理所有缓存吗？')) {
      try {
        await clearCache()
        showMessage('success', '缓存已清理')
      } catch (error) {
        showMessage('error', '清理缓存失败')
      }
    }
  }

  const handleLogout = async () => {
    if (confirm('确定要退出登录吗？')) {
      try {
        await logout()
        clearUserData() // 清理用户数据
        onClose()
      } catch (error) {
        showMessage('error', '退出登录失败')
      }
    }
  }

  const handleRefreshPhoto = async () => {
    try {
      const photo = await loadUserPhoto('120x120')
      if (photo) {
        showMessage('success', '头像刷新成功')
      } else {
        showMessage('error', '无法获取用户头像')
      }
    } catch (error) {
      showMessage('error', '刷新头像失败')
    }
  }

  // 多账号管理功能已删除



  if (!isVisible) return null

  // 如果配置还没加载完成，显示加载状态
  if (!config) {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '40px',
          textAlign: 'center'
        }}>
          <div>加载配置中...</div>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'var(--bg-modal)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'var(--bg-primary)',
        borderRadius: '12px',
        width: '800px',
        height: '600px',
        display: 'flex',
        overflow: 'hidden',
        boxShadow: 'var(--shadow-large)'
      }}>
        {/* 侧边栏 */}
        <div style={{
          width: '200px',
          backgroundColor: 'var(--bg-secondary)',
          borderRight: '1px solid var(--border-primary)',
          padding: '20px 0'
        }}>
          <div style={{
            padding: '0 20px 20px',
            borderBottom: '1px solid var(--border-primary)',
            marginBottom: '20px'
          }}>
            <h2 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: 'var(--text-primary)',
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <SettingsIcon size={20} />
              设置
            </h2>
          </div>

          <div style={{ padding: '0 10px' }}>
            {[
              { id: 'account', label: '账户', icon: User },
              { id: 'general', label: '通用', icon: Monitor },
              { id: 'sync', label: '同步', icon: Upload },
              { id: 'about', label: '关于', icon: Info }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: 'none',
                  backgroundColor: activeTab === tab.id ? 'var(--bg-accent)' : 'transparent',
                  color: activeTab === tab.id ? 'var(--text-inverse)' : 'var(--text-primary)',
                  borderRadius: '8px',
                  marginBottom: '4px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
              >
                <tab.icon size={16} />
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* 主内容区 */}
        <div style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* 内容区域 */}
          <div style={{
            flex: 1,
            padding: '30px',
            overflow: 'auto'
          }}>
            {/* 消息提示 */}
            {message && (
              <div style={{
                padding: '12px 16px',
                borderRadius: '8px',
                marginBottom: '20px',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: message.type === 'success' ? '#d4edda' : '#f8d7da',
                color: message.type === 'success' ? '#155724' : '#721c24',
                border: `1px solid ${message.type === 'success' ? '#c3e6cb' : '#f5c6cb'}`
              }}>
                {message.text}
              </div>
            )}

            {activeTab === 'account' && (
              <div>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '20px' }}>账户信息</h3>

                {/* 用户基本信息 */}
                <div style={{
                  marginBottom: '30px',
                  padding: '20px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '12px',
                  border: '1px solid #e9ecef'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '16px' }}>
                    <div style={{
                      width: '60px',
                      height: '60px',
                      borderRadius: '50%',
                      overflow: 'hidden',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: profile?.photo ? 'transparent' : '#007aff',
                      color: 'white',
                      fontSize: '24px',
                      fontWeight: '600'
                    }}>
                      {profile?.photo ? (
                        <img
                          src={profile.photo}
                          alt="用户头像"
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover'
                          }}
                          onError={(e) => {
                            e.currentTarget.style.display = 'none'
                            e.currentTarget.parentElement!.style.background = '#007aff'
                            e.currentTarget.parentElement!.innerHTML = (profile?.displayName || user?.displayName)?.charAt(0) || 'U'
                          }}
                        />
                      ) : (
                        (profile?.displayName || user?.displayName)?.charAt(0) || 'U'
                      )}
                    </div>
                    <div style={{ flex: 1 }}>
                      <h4 style={{
                        fontSize: '18px',
                        fontWeight: '600',
                        margin: '0 0 4px 0',
                        color: '#1d1d1f'
                      }}>
                        {profile?.displayName || user?.displayName || '未知用户'}
                      </h4>
                      <p style={{
                        fontSize: '14px',
                        color: '#6d6d70',
                        margin: '0 0 8px 0'
                      }}>
                        {user?.userPrincipalName || user?.mail || '未知邮箱'}
                      </p>
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <button
                          onClick={handleRefreshPhoto}
                          style={{
                            padding: '6px 12px',
                            border: '1px solid #007aff',
                            borderRadius: '6px',
                            backgroundColor: 'white',
                            color: '#007aff',
                            cursor: 'pointer',
                            fontSize: '12px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px'
                          }}
                        >
                          <RefreshCw size={12} />
                          刷新头像
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 存储空间信息 */}
                {storageInfo && storageInfo.quota && storageInfo.quota.total > 0 ? (
                  <div style={{
                    marginBottom: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '12px',
                    border: '1px solid #e9ecef'
                  }}>
                    <h5 style={{
                      fontSize: '14px',
                      fontWeight: '600',
                      marginBottom: '16px',
                      color: '#1d1d1f'
                    }}>
                      存储空间
                    </h5>
                    <div style={{ marginBottom: '12px' }}>
                      <div style={{
                        width: '100%',
                        height: '8px',
                        backgroundColor: '#e9ecef',
                        borderRadius: '4px',
                        overflow: 'hidden'
                      }}>
                        <div style={{
                          width: `${getStoragePercentage(storageInfo)}%`,
                          height: '100%',
                          backgroundColor: getStoragePercentage(storageInfo) > 80 ? '#ff3b30' : '#007aff',
                          transition: 'width 0.3s ease'
                        }} />
                      </div>
                    </div>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      fontSize: '13px',
                      color: '#6d6d70'
                    }}>
                      <span>已使用: {formatBytes(storageInfo.quota.used)}</span>
                      <span>总容量: {formatBytes(storageInfo.quota.total)}</span>
                    </div>
                    <div style={{
                      fontSize: '12px',
                      color: '#6d6d70',
                      marginTop: '8px',
                      textAlign: 'center'
                    }}>
                      剩余空间: {formatBytes(storageInfo.quota.remaining)} ({(100 - getStoragePercentage(storageInfo)).toFixed(1)}%)
                    </div>
                  </div>
                ) : (
                  <div style={{
                    marginBottom: '30px',
                    padding: '20px',
                    backgroundColor: '#fff8e1',
                    borderRadius: '12px',
                    border: '1px solid #ffcc02',
                    textAlign: 'center'
                  }}>
                    <h5 style={{
                      fontSize: '14px',
                      fontWeight: '600',
                      marginBottom: '8px',
                      color: '#1d1d1f'
                    }}>
                      存储空间信息
                    </h5>
                    <p style={{
                      fontSize: '13px',
                      color: '#6d6d70',
                      margin: '0 0 12px 0',
                      lineHeight: '1.4'
                    }}>
                      {userLoading ? '正在加载存储信息...' : '暂时无法获取存储空间信息'}
                    </p>
                    {!userLoading && (
                      <button
                        onClick={() => {
                          console.log('手动刷新存储信息')
                          loadUserData()
                        }}
                        style={{
                          padding: '6px 12px',
                          border: '1px solid #ffcc02',
                          borderRadius: '6px',
                          backgroundColor: 'white',
                          color: '#b8860b',
                          cursor: 'pointer',
                          fontSize: '12px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px',
                          margin: '0 auto'
                        }}
                      >
                        <RefreshCw size={12} />
                        重新加载
                      </button>
                    )}
                  </div>
                )}

                {/* 账户操作 */}
                <div style={{
                  padding: '20px',
                  backgroundColor: '#fff5f5',
                  borderRadius: '12px',
                  border: '1px solid #fed7d7'
                }}>
                  <h5 style={{
                    fontSize: '14px',
                    fontWeight: '600',
                    marginBottom: '12px',
                    color: '#1d1d1f'
                  }}>
                    账户操作
                  </h5>
                  <p style={{
                    fontSize: '13px',
                    color: '#6d6d70',
                    marginBottom: '16px',
                    lineHeight: '1.4'
                  }}>
                    退出登录将清除本地缓存的登录信息，下次使用需要重新登录。
                  </p>
                  <button
                    onClick={handleLogout}
                    style={{
                      padding: '10px 16px',
                      border: '1px solid #ff3b30',
                      borderRadius: '6px',
                      backgroundColor: 'white',
                      color: '#ff3b30',
                      cursor: 'pointer',
                      fontSize: '14px',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px'
                    }}
                  >
                    <LogOut size={14} />
                    退出登录
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'general' && (
              <div>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '20px' }}>通用设置</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                    主题
                  </label>
                  <select
                    value={config.theme}
                    onChange={(e) => handleSettingChange('theme', e.target.value)}
                    style={{
                      width: '200px',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px'
                    }}
                  >
                    <option value="auto">跟随系统</option>
                    <option value="light">浅色</option>
                    <option value="dark">深色</option>
                  </select>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                    语言
                  </label>
                  <select
                    value={config.language}
                    onChange={(e) => handleSettingChange('language', e.target.value)}
                    style={{
                      width: '200px',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px'
                    }}
                  >
                    <option value="zh-CN">简体中文</option>
                    <option value="en-US">English</option>
                  </select>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.startWithWindows}
                      onChange={(e) => handleSettingChange('startWithWindows', e.target.checked)}
                    />
                    开机自启动
                  </label>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.minimizeToTray}
                      onChange={(e) => handleSettingChange('minimizeToTray', e.target.checked)}
                    />
                    最小化到系统托盘
                  </label>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.showNotifications}
                      onChange={(e) => handleSettingChange('showNotifications', e.target.checked)}
                    />
                    显示通知
                  </label>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.rememberLogin}
                      onChange={(e) => handleSettingChange('rememberLogin', e.target.checked)}
                    />
                    记住登录状态
                  </label>
                  <div style={{ fontSize: '12px', color: '#6d6d70', marginTop: '4px', marginLeft: '24px' }}>
                    启用后将保存登录信息，下次启动时自动登录
                  </div>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.developerMode}
                      onChange={(e) => handleSettingChange('developerMode', e.target.checked)}
                    />
                    开发者模式
                  </label>
                  <div style={{ fontSize: '12px', color: '#6d6d70', marginTop: '4px', marginLeft: '24px' }}>
                    开启后可使用 Ctrl+Shift+I 或 F12 打开开发者工具
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'sync' && (
              <div>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '20px' }}>同步设置</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.autoSync}
                      onChange={(e) => handleSettingChange('autoSync', e.target.checked)}
                    />
                    自动同步
                  </label>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                    同步间隔（分钟）
                  </label>
                  <input
                    type="number"
                    value={config.syncInterval}
                    onChange={(e) => handleSettingChange('syncInterval', parseInt(e.target.value) || 30)}
                    min="1"
                    max="1440"
                    style={{
                      width: '120px',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px'
                    }}
                  />
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                    下载路径
                  </label>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    <input
                      type="text"
                      value={config.downloadPath}
                      onChange={(e) => handleSettingChange('downloadPath', e.target.value)}
                      placeholder="选择下载文件夹..."
                      style={{
                        flex: 1,
                        padding: '8px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px'
                      }}
                    />
                    <button
                      onClick={handleSelectDownloadPath}
                      style={{
                        padding: '8px 12px',
                        border: '1px solid #007aff',
                        borderRadius: '6px',
                        backgroundColor: 'white',
                        color: '#007aff',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}
                    >
                      <Folder size={14} />
                      浏览
                    </button>
                  </div>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                    冲突处理
                  </label>
                  <select
                    value={config.conflictResolution}
                    onChange={(e) => handleSettingChange('conflictResolution', e.target.value)}
                    style={{
                      width: '200px',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px'
                    }}
                  >
                    <option value="ask">询问我</option>
                    <option value="overwrite">覆盖</option>
                    <option value="skip">跳过</option>
                  </select>
                </div>
              </div>
            )}

            {/* 多账号管理功能已删除 */}

            {activeTab === 'about' && (
              <div>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '20px' }}>关于 OD-Commander</h3>
                
                <div style={{ textAlign: 'center', marginBottom: '30px' }}>
                  <img 
                    src="/assets/icon.svg" 
                    alt="OD-Commander" 
                    style={{ width: '64px', height: '64px', marginBottom: '16px' }}
                  />
                  <h4 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '8px' }}>
                    OD-Commander
                  </h4>
                  <p style={{ fontSize: '14px', color: '#6d6d70', marginBottom: '4px' }}>
                    版本 1.0.0
                  </p>
                  <p style={{ fontSize: '14px', color: '#6d6d70' }}>
                    现代化的 OneDrive 客户端
                  </p>
                </div>

                <div style={{ fontSize: '14px', lineHeight: '1.6', color: '#1d1d1f' }}>
                  <p>
                    OD-Commander 是一个现代化的第三方 OneDrive 客户端，提供流畅的文件管理体验。
                  </p>
                  <p>
                    基于 Electron + React + TypeScript 构建，支持文件上传、下载、同步等功能。
                  </p>
                </div>

                <div style={{ marginTop: '30px', padding: '16px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                  <h5 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                    技术栈
                  </h5>
                  <ul style={{ fontSize: '13px', color: '#6d6d70', margin: 0, paddingLeft: '20px' }}>
                    <li>Electron 框架</li>
                    <li>React + TypeScript</li>
                    <li>Fluent UI React v9</li>
                    <li>Microsoft Graph API</li>
                    <li>Zustand 状态管理</li>
                  </ul>
                </div>

                <div style={{ marginTop: '20px', padding: '16px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                  <h5 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '12px' }}>
                    配置管理
                  </h5>
                  <div style={{ fontSize: '13px', color: '#6d6d70', marginBottom: '12px' }}>
                    配置文件位置：{configPath}
                  </div>
                  <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                    <button
                      onClick={handleExportConfig}
                      style={{
                        padding: '6px 12px',
                        border: '1px solid #6c757d',
                        borderRadius: '4px',
                        backgroundColor: 'white',
                        color: '#6c757d',
                        cursor: 'pointer',
                        fontSize: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}
                    >
                      <FileDown size={12} />
                      导出配置
                    </button>
                    <button
                      onClick={handleImportConfig}
                      style={{
                        padding: '6px 12px',
                        border: '1px solid #6c757d',
                        borderRadius: '4px',
                        backgroundColor: 'white',
                        color: '#6c757d',
                        cursor: 'pointer',
                        fontSize: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}
                    >
                      <FileUp size={12} />
                      导入配置
                    </button>
                    <button
                      onClick={handleClearCache}
                      style={{
                        padding: '6px 12px',
                        border: '1px solid #ff9500',
                        borderRadius: '4px',
                        backgroundColor: 'white',
                        color: '#ff9500',
                        cursor: 'pointer',
                        fontSize: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}
                    >
                      <Trash2 size={12} />
                      清理缓存
                    </button>
                  </div>
                </div>

                <div style={{ textAlign: 'center', fontSize: '12px', color: '#8e8e93' }}>
                  配置路径: {configPath}
                </div>
              </div>
            )}
          </div>

          {/* 底部按钮 */}
          <div style={{
            padding: '20px 30px',
            borderTop: '1px solid #e1e1e6',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <button
              onClick={handleReset}
              className="reset-button"
              style={{
                padding: '10px 16px',
                border: '1px solid #ff3b30',
                borderRadius: '6px',
                backgroundColor: 'white',
                color: '#ff3b30',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
              }}
            >
              <RotateCcw size={14} />
              重置
            </button>

            <div style={{ display: 'flex', gap: '12px' }}>
              <button
                onClick={onClose}
                style={{
                  padding: '8px 20px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  backgroundColor: 'white',
                  color: '#1d1d1f',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                style={{
                  padding: '8px 20px',
                  border: 'none',
                  borderRadius: '6px',
                  backgroundColor: '#007aff',
                  color: 'white',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                <Save size={14} />
                Save
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings