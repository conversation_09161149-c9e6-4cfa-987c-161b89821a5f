import { create } from 'zustand'

export interface AppConfig {
  // 通用设置
  theme: 'auto' | 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
  startWithWindows: boolean
  minimizeToTray: boolean
  showNotifications: boolean
  
  // 同步设置
  autoSync: boolean
  syncInterval: number // 分钟
  downloadPath: string
  uploadQuality: 'original' | 'compressed'
  conflictResolution: 'ask' | 'overwrite' | 'skip'
  
  // 高级设置
  maxConcurrentUploads: number
  maxConcurrentDownloads: number
  cacheSize: number // MB
  enableLogging: boolean
  
  // 开发者设置
  developerMode: boolean
  
  // 窗口设置
  windowBounds: {
    width: number
    height: number
    x?: number
    y?: number
  }
  
  // 自定义配置路径
  customConfigPath?: string
}

interface ConfigState {
  config: AppConfig | null
  isLoading: boolean
  error: string | null
  
  // Actions
  loadConfig: () => Promise<void>
  updateConfig: (updates: Partial<AppConfig>) => Promise<void>
  resetConfig: () => Promise<void>
  getConfigPath: () => Promise<string>
  setCustomConfigPath: (path: string) => Promise<void>
  exportConfig: (path: string) => Promise<void>
  importConfig: (path: string) => Promise<void>
  clearCache: () => Promise<void>
}

export const useConfigStore = create<ConfigState>((set, get) => ({
  config: null,
  isLoading: false,
  error: null,

  loadConfig: async () => {
    set({ isLoading: true, error: null })
    try {
      const config = await window.electronAPI.config.get()
      set({ config, isLoading: false })
    } catch (error) {
      console.error('加载配置失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '加载配置失败', 
        isLoading: false 
      })
    }
  },

  updateConfig: async (updates: Partial<AppConfig>) => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.config.update(updates)
      if (result.success) {
        const currentConfig = get().config
        if (currentConfig) {
          const newConfig = { ...currentConfig, ...updates }
          set({ config: newConfig, isLoading: false })
        }
      } else {
        throw new Error(result.error || '更新配置失败')
      }
    } catch (error) {
      console.error('更新配置失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '更新配置失败', 
        isLoading: false 
      })
      throw error
    }
  },

  resetConfig: async () => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.config.reset()
      if (result.success) {
        // 重新加载配置
        await get().loadConfig()
      } else {
        throw new Error(result.error || '重置配置失败')
      }
    } catch (error) {
      console.error('重置配置失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '重置配置失败', 
        isLoading: false 
      })
      throw error
    }
  },

  getConfigPath: async () => {
    try {
      return await window.electronAPI.config.getPath()
    } catch (error) {
      console.error('获取配置路径失败:', error)
      throw error
    }
  },

  setCustomConfigPath: async (path: string) => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.config.setCustomPath(path)
      if (result.success) {
        // 重新加载配置
        await get().loadConfig()
      } else {
        throw new Error(result.error || '设置配置路径失败')
      }
    } catch (error) {
      console.error('设置配置路径失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '设置配置路径失败', 
        isLoading: false 
      })
      throw error
    }
  },

  exportConfig: async (path: string) => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.config.export(path)
      if (!result.success) {
        throw new Error(result.error || '导出配置失败')
      }
      set({ isLoading: false })
    } catch (error) {
      console.error('导出配置失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '导出配置失败', 
        isLoading: false 
      })
      throw error
    }
  },

  importConfig: async (path: string) => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.config.import(path)
      if (result.success) {
        // 重新加载配置
        await get().loadConfig()
      } else {
        throw new Error(result.error || '导入配置失败')
      }
    } catch (error) {
      console.error('导入配置失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '导入配置失败', 
        isLoading: false 
      })
      throw error
    }
  },

  clearCache: async () => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.config.clearCache()
      if (!result.success) {
        throw new Error(result.error || '清理缓存失败')
      }
      set({ isLoading: false })
    } catch (error) {
      console.error('清理缓存失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '清理缓存失败', 
        isLoading: false 
      })
      throw error
    }
  }
})) 