import { create } from 'zustand'

interface DriveItem {
  id: string
  name: string
  size?: number
  lastModifiedDateTime: string
  folder?: { childCount: number }
  file?: { mimeType: string }
  webUrl: string
}

interface FileState {
  currentPath: string
  files: DriveItem[]
  selectedFiles: string[]
  isLoading: boolean
  error: string | null
  
  // Actions
  loadFiles: () => Promise<void>
  navigateToFolder: (folderId: string, folderName: string) => void
  navigateToRoot: () => void
  navigateToPath: (path: string) => void
  navigateUp: () => void
  toggleFileSelection: (fileId: string) => void
  clearSelection: () => void
  uploadFile: (file: File) => Promise<void>
  downloadFile: (fileId: string) => Promise<void>
  deleteFiles: (fileIds: string[]) => Promise<void>
  createFolder: (name: string) => Promise<void>
}

export const useFileStore = create<FileState>((set, get) => ({
  currentPath: '',
  files: [],
  selectedFiles: [],
  isLoading: false,
  error: null,

  loadFiles: async () => {
    const { currentPath } = get()
    set({ isLoading: true, error: null })
    
    try {
      const result = await window.electronAPI.files.list(currentPath)
      
      if (result.success) {
        set({ 
          files: result.items || [],
          isLoading: false 
        })
      } else {
        set({ 
          error: result.error || '加载文件失败',
          isLoading: false 
        })
      }
    } catch (error) {
      console.error('Failed to load files:', error)
      set({ 
        error: '加载文件时发生错误',
        isLoading: false 
      })
    }
  },

  navigateToFolder: (folderId: string, folderName: string) => {
    const { currentPath } = get()
    const newPath = currentPath ? `${currentPath}/${folderName}` : folderName
    set({ 
      currentPath: newPath,
      selectedFiles: []
    })
  },

  navigateToRoot: () => {
    set({ 
      currentPath: '',
      selectedFiles: []
    })
  },

  navigateToPath: (path: string) => {
    set({ 
      currentPath: path,
      selectedFiles: []
    })
  },

  navigateUp: () => {
    const { currentPath } = get()
    if (!currentPath) return
    
    const pathParts = currentPath.split('/').filter(Boolean)
    pathParts.pop()
    const newPath = pathParts.join('/')
    
    set({ 
      currentPath: newPath,
      selectedFiles: []
    })
  },

  toggleFileSelection: (fileId: string) => {
    const { selectedFiles } = get()
    const isSelected = selectedFiles.includes(fileId)
    
    if (isSelected) {
      set({ 
        selectedFiles: selectedFiles.filter(id => id !== fileId) 
      })
    } else {
      set({ 
        selectedFiles: [...selectedFiles, fileId] 
      })
    }
  },

  clearSelection: () => {
    set({ selectedFiles: [] })
  },

  uploadFile: async (file: File) => {
    const { currentPath } = get()
    
    try {
      // Read file as buffer
      const fileBuffer = await file.arrayBuffer()
      const uint8Array = new Uint8Array(fileBuffer)
      
      // Upload file data directly
      const result = await window.electronAPI.files.uploadBuffer(
        Array.from(uint8Array), // Convert to regular array for IPC
        file.name,
        currentPath
      )

      if (!result.success) {
        throw new Error(result.error || `Failed to upload ${file.name}`)
      }
    } catch (error) {
      console.error('Upload failed:', error)
      throw error
    }
  },

  downloadFile: async (fileId: string) => {
    try {
      // Show folder picker dialog
      const result = await window.electronAPI.showSaveDialog({
        title: '选择下载位置',
        defaultPath: 'Downloads',
        properties: ['openDirectory']
      })

      if (result.canceled) return

      const downloadPath = result.filePath
      
      const downloadResult = await window.electronAPI.files.download(
        fileId,
        downloadPath
      )

      if (!downloadResult.success) {
        throw new Error(downloadResult.error || `Failed to download file ${fileId}`)
      }
    } catch (error) {
      console.error('Download failed:', error)
      throw error
    }
  },

  deleteFiles: async (fileIds: string[]) => {
    try {
      for (const fileId of fileIds) {
        const result = await window.electronAPI.files.delete(fileId)

        if (!result.success) {
          throw new Error(result.error || `Failed to delete file ${fileId}`)
        }
      }
      
      // Clear selection after successful deletion
      set({ selectedFiles: [] })
    } catch (error) {
      console.error('Delete failed:', error)
      throw error
    }
  },

  createFolder: async (name: string) => {
    const { currentPath } = get()
    
    try {
      const result = await window.electronAPI.files.createFolder(
        name,
        currentPath
      )

      if (!result.success) {
        throw new Error(result.error || '创建文件夹失败')
      }
    } catch (error) {
      console.error('Create folder failed:', error)
      throw error
    }
  }
})) 