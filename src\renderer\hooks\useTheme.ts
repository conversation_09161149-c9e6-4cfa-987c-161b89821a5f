import { useState, useEffect } from 'react'
import { useConfigStore } from '../store/configStore'

export type Theme = 'light' | 'dark'
export type ThemeConfig = 'auto' | 'light' | 'dark'

interface UseThemeReturn {
  theme: Theme
  themeConfig: ThemeConfig
  setThemeConfig: (config: ThemeConfig) => void
  isDark: boolean
  isLight: boolean
}

export function useTheme(): UseThemeReturn {
  const { config, updateConfig } = useConfigStore()
  const [systemTheme, setSystemTheme] = useState<Theme>('light')

  // 监听系统主题变化
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const updateSystemTheme = () => {
      setSystemTheme(mediaQuery.matches ? 'dark' : 'light')
    }
    
    // 初始化系统主题
    updateSystemTheme()
    
    // 监听系统主题变化
    mediaQuery.addListener(updateSystemTheme)
    
    return () => {
      mediaQuery.removeListener(updateSystemTheme)
    }
  }, [])

  // 计算当前生效的主题
  const theme: Theme = config?.theme === 'auto' ? systemTheme : (config?.theme as Theme) || 'light'
  
  const setThemeConfig = async (themeConfig: ThemeConfig) => {
    try {
      await updateConfig({ theme: themeConfig })
    } catch (error) {
      console.error('更新主题配置失败:', error)
    }
  }

  return {
    theme,
    themeConfig: config?.theme || 'auto',
    setThemeConfig,
    isDark: theme === 'dark',
    isLight: theme === 'light'
  }
} 