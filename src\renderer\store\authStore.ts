import { create } from 'zustand'

interface User {
  id: string
  displayName: string
  userPrincipalName: string
  mail?: string
}

interface AuthState {
  isAuthenticated: boolean
  user: User | null
  accessToken: string | null
  isAutoLogin: boolean
  login: (forceNewLogin?: boolean) => Promise<boolean>
  logout: () => Promise<void>
  autoLogin: () => Promise<boolean>
  setUser: (user: User, token: string) => void
  // 多账号同步功能已删除
}

export const useAuthStore = create<AuthState>((set, get) => ({
  isAuthenticated: false,
  user: null,
  accessToken: null,
  isAutoLogin: false,

  login: async (forceNewLogin: boolean = false) => {
    try {
      const result = await window.electronAPI.auth.login(forceNewLogin)
      
      if (result.success && result.userInfo && result.accessToken) {
        set({
          isAuthenticated: true,
          user: {
            id: result.userInfo.id,
            displayName: result.userInfo.displayName || result.userInfo.name,
            userPrincipalName: result.userInfo.userPrincipalName || result.userInfo.email,
            mail: result.userInfo.mail || result.userInfo.email
          },
          accessToken: result.accessToken
        })
        return true
      } else {
        throw new Error(result.error || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  },

  logout: async () => {
    try {
      const result = await window.electronAPI.auth.logout()
      
      if (result.success) {
        set({
          isAuthenticated: false,
          user: null,
          accessToken: null,
          isAutoLogin: false
        })
      } else {
        throw new Error(result.error || 'Logout failed')
      }
    } catch (error) {
      console.error('Logout error:', error)
      throw error
    }
  },

  autoLogin: async () => {
    try {
      // 首先检查会话状态
      const sessionCheck = await window.electronAPI.auth.checkSession()

      if (!sessionCheck.success || !sessionCheck.hasValidSession) {
        console.log('No valid session found for auto-login')
        return false
      }

      console.log('Valid session found, attempting auto-login...')
      const result = await window.electronAPI.auth.autoLogin()

      if (result.success && result.userInfo && result.accessToken) {
        set({
          isAuthenticated: true,
          user: {
            id: result.userInfo.id,
            displayName: result.userInfo.displayName || result.userInfo.name,
            userPrincipalName: result.userInfo.userPrincipalName || result.userInfo.email,
            mail: result.userInfo.mail || result.userInfo.email
          },
          accessToken: result.accessToken,
          isAutoLogin: true
        })
        console.log('Auto-login successful')
        return true
      } else {
        console.log('Auto login failed:', result.error)
        return false
      }
    } catch (error) {
      console.error('Auto login error:', error)
      return false
    }
  },

  setUser: (user: User, token: string) => {
    set({
      isAuthenticated: true,
      user,
      accessToken: token
    })
  },

  // 多账号同步功能已删除
})) 