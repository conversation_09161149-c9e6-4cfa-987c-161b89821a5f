import { create } from 'zustand'

export interface UserProfile {
  id: string
  displayName: string
  userPrincipalName: string
  mail: string
  jobTitle?: string
  officeLocation?: string
  businessPhones: string[]
  mobilePhone?: string
  photo?: string
}

export interface StorageInfo {
  quota: {
    total: number
    used: number
    remaining: number
    deleted: number
    state: string
  }
  driveType?: string
}

interface UserState {
  profile: UserProfile | null
  storageInfo: StorageInfo | null
  isLoading: boolean
  error: string | null
  
  // Actions
  loadProfile: () => Promise<void>
  loadStorageInfo: () => Promise<void>
  loadUserData: () => Promise<void>
  loadUserPhoto: (size?: string) => Promise<string | null>
  clearUserData: () => void
}

export const useUserStore = create<UserState>((set, get) => ({
  profile: null,
  storageInfo: null,
  isLoading: false,
  error: null,

  loadProfile: async () => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.user.getProfile()
      if (result.success) {
        set({ profile: result.profile, isLoading: false })
      } else {
        throw new Error(result.error || '获取用户资料失败')
      }
    } catch (error) {
      console.error('加载用户资料失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '加载用户资料失败', 
        isLoading: false 
      })
    }
  },

  loadStorageInfo: async () => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.user.getStorageInfo()
      if (result.success) {
        console.log('存储信息获取成功:', result.storageInfo)
        set({ storageInfo: result.storageInfo, isLoading: false })
      } else {
        console.error('存储信息获取失败:', result.error)
        // 即使失败也设置一个默认的存储信息，避免显示"无法获取"
        set({ 
          storageInfo: {
            quota: {
              total: 0,
              used: 0,
              remaining: 0,
              deleted: 0,
              state: 'error'
            },
            driveType: 'error'
          },
          isLoading: false 
        })
      }
    } catch (error) {
      console.error('加载存储信息失败:', error)
      set({ 
        storageInfo: {
          quota: {
            total: 0,
            used: 0,
            remaining: 0,
            deleted: 0,
            state: 'error'
          },
          driveType: 'error'
        },
        isLoading: false 
      })
    }
  },

  loadUserData: async () => {
    set({ isLoading: true, error: null })
    try {
      // 并行加载用户资料和存储信息
      const [profileResult, storageResult] = await Promise.all([
        window.electronAPI.user.getProfile(),
        window.electronAPI.user.getStorageInfo()
      ])

      const updates: Partial<UserState> = { isLoading: false }

      if (profileResult.success) {
        updates.profile = profileResult.profile
        console.log('用户资料获取成功:', profileResult.profile)
      } else {
        console.error('获取用户资料失败:', profileResult.error)
      }

      if (storageResult.success) {
        updates.storageInfo = storageResult.storageInfo
        console.log('存储信息获取成功:', storageResult.storageInfo)
      } else {
        console.error('获取存储信息失败:', storageResult.error)
        // 设置默认存储信息
        updates.storageInfo = {
          quota: {
            total: 0,
            used: 0,
            remaining: 0,
            deleted: 0,
            state: 'error'
          },
          driveType: 'error'
        }
      }

      set(updates)
    } catch (error) {
      console.error('加载用户数据失败:', error)
      set({ 
        storageInfo: {
          quota: {
            total: 0,
            used: 0,
            remaining: 0,
            deleted: 0,
            state: 'error'
          },
          driveType: 'error'
        },
        isLoading: false 
      })
    }
  },

  loadUserPhoto: async (size = '120x120') => {
    try {
      const result = await window.electronAPI.user.getPhoto(size)
      if (result.success && result.photo) {
        // 更新profile中的photo字段
        const currentProfile = get().profile
        if (currentProfile) {
          set({ 
            profile: { 
              ...currentProfile, 
              photo: result.photo 
            } 
          })
        }
        return result.photo
      } else {
        console.log('获取用户头像失败:', result.error)
        return null
      }
    } catch (error) {
      console.error('加载用户头像失败:', error)
      return null
    }
  },

  clearUserData: () => {
    set({
      profile: null,
      storageInfo: null,
      isLoading: false,
      error: null
    })
  }
}))

// 格式化存储大小的工具函数
export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 计算存储使用百分比
export const getStoragePercentage = (storageInfo: StorageInfo | null): number => {
  if (!storageInfo || storageInfo.quota.total === 0) return 0
  return Math.round((storageInfo.quota.used / storageInfo.quota.total) * 100)
} 