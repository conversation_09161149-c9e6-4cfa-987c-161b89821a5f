# OD-Commander

A modern, powerful OneDrive client for Windows built with Electron, React, and TypeScript.

## Features

- 🔐 **Secure OAuth Authentication** - Device code flow for secure authentication
- 📁 **File Management** - Browse, upload, download, and manage OneDrive files
- 🎨 **Modern UI** - Built with Microsoft Fluent UI for a native Windows experience
- ⚡ **Fast Performance** - Electron + Vite for optimal performance
- 🔄 **Sync Capabilities** - Custom folder synchronization (coming soon)

## Prerequisites

- Node.js 16 or higher
- Windows 10/11
- Microsoft Azure account (for app registration)

## Setup Instructions

### 1. Clone and Install

```bash
git clone <repository-url>
cd OD-Commander
npm install
```

### 2. Microsoft App Registration

Before you can use the application, you need to register it with Microsoft Azure:

1. Go to [Azure Portal - App Registrations](https://portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationsListBlade)
2. Click **"New registration"**
3. Fill in the details:
   - **Name**: `OD-Commander`
   - **Supported account types**: `Accounts in any organizational directory and personal Microsoft accounts`
   - **Redirect URI**: `http://localhost:3000/auth/callback` (Web)
4. Click **"Register"**
5. Copy the **Application (client) ID**
6. Go to **"Authentication"** tab:
   - Enable **"Allow public client flows"**
7. Go to **"API permissions"** tab:
   - Add **Microsoft Graph** permissions:
     - `Files.ReadWrite.All` (Delegated)
     - `User.Read` (Delegated)
   - Click **"Grant admin consent"** (if you're an admin)

### 3. Configure the Application

1. Open `src/main/config.ts`
2. Replace `'your-client-id-here'` with your actual Application (client) ID from step 2

```typescript
export const APP_CONFIG = {
  auth: {
    clientId: 'your-actual-client-id-here', // Replace this
    authority: 'https://login.microsoftonline.com/common',
    redirectUri: 'http://localhost:3000/auth/callback'
  },
  // ... rest of config
}
```

## Development

### Start Development Server

```bash
npm run dev
```

This will start the Electron application in development mode with hot reload.

### Build for Production

```bash
npm run build
```

### Package the Application

```bash
npm run package
```

This will create a packaged version of the application in the `dist/` directory.

## Usage

1. **Launch the Application**: Run `npm run dev` or launch the packaged application
2. **Authenticate**: Click "Connect to OneDrive" and follow the device code authentication flow
3. **Browse Files**: Use the file explorer to navigate your OneDrive
4. **Manage Files**: Upload, download, create folders, and manage your files

## Authentication Flow

The application uses Microsoft's device code authentication flow, which is secure and doesn't require storing client secrets:

1. Click "Connect to OneDrive"
2. A dialog will appear with a device code
3. Open the provided URL in your browser
4. Enter the device code
5. Sign in with your Microsoft account
6. Grant permissions to the application
7. Return to the application - you're now authenticated!

## Project Structure

```
src/
├── main/                 # Electron main process
│   ├── services/        # Authentication and Graph API services
│   ├── config.ts        # Application configuration
│   └── index.ts         # Main process entry point
├── preload/             # Electron preload scripts
│   ├── index.ts         # IPC API definitions
│   └── index.d.ts       # TypeScript definitions
└── renderer/            # React frontend
    ├── components/      # React components
    ├── stores/          # Zustand state management
    ├── utils/           # Utility functions
    ├── App.tsx          # Main React component
    └── main.tsx         # React entry point
```

## Technologies Used

- **Electron** - Cross-platform desktop app framework
- **React** - UI library
- **TypeScript** - Type-safe JavaScript
- **Vite** - Fast build tool
- **Fluent UI** - Microsoft's design system
- **Zustand** - State management
- **MSAL Node** - Microsoft Authentication Library
- **Microsoft Graph** - OneDrive API client

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

If you encounter any issues:

1. Check that your Microsoft App Registration is configured correctly
2. Ensure you have the required permissions
3. Check the console for error messages
4. Create an issue on GitHub with detailed information

## Roadmap

- [ ] File preview functionality
- [ ] Custom folder synchronization
- [ ] Batch operations
- [ ] Search functionality
- [ ] Settings and preferences
- [ ] Dark theme support
- [ ] Multi-account support 