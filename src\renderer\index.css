/* 简约现代化全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* 亮色主题变量 */
  --bg-primary: #ffffff;
  --bg-secondary: #f9f9f9;
  --bg-tertiary: #f2f2f7;
  --bg-quaternary: #e5e5ea;
  --bg-accent: #007aff;
  --bg-accent-hover: #0056cc;
  --bg-success: #34c759;
  --bg-success-hover: #28a745;
  --bg-danger: #ff3b30;
  --bg-danger-hover: #d70015;
  --bg-modal: rgba(0, 0, 0, 0.4);
  
  --text-primary: #1d1d1f;
  --text-secondary: #6d6d70;
  --text-tertiary: #8e8e93;
  --text-accent: #007aff;
  --text-accent-hover: #0056cc;
  --text-inverse: #ffffff;
  --text-success: #155724;
  --text-danger: #721c24;
  
  --border-primary: #e5e5e7;
  --border-secondary: #d1d1d6;
  --border-tertiary: #c7c7cc;
  --border-focus: rgba(0, 122, 255, 0.1);
  
  --shadow-small: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 10px 40px rgba(0, 0, 0, 0.15);
  --shadow-large: 0 20px 40px rgba(0, 0, 0, 0.15);
  
  --scrollbar-track: #f5f5f7;
  --scrollbar-thumb: #c7c7cc;
  --scrollbar-thumb-hover: #a1a1a6;
  --scrollbar-thumb-active: #8e8e93;
  
  --message-success-bg: #d4edda;
  --message-success-border: #c3e6cb;
  --message-error-bg: #f8d7da;
  --message-error-border: #f5c6cb;
  
  --selection-bg: rgba(0, 122, 255, 0.2);
}

[data-theme="dark"] {
  /* 暗色主题变量 */
  --bg-primary: #1c1c1e;
  --bg-secondary: #2c2c2e;
  --bg-tertiary: #3a3a3c;
  --bg-quaternary: #48484a;
  --bg-accent: #0a84ff;
  --bg-accent-hover: #409cff;
  --bg-success: #30d158;
  --bg-success-hover: #32d74b;
  --bg-danger: #ff453a;
  --bg-danger-hover: #ff6961;
  --bg-modal: rgba(0, 0, 0, 0.6);
  
  --text-primary: #ffffff;
  --text-secondary: #aeaeb2;
  --text-tertiary: #8e8e93;
  --text-accent: #0a84ff;
  --text-accent-hover: #409cff;
  --text-inverse: #000000;
  --text-success: #30d158;
  --text-danger: #ff453a;
  
  --border-primary: #38383a;
  --border-secondary: #48484a;
  --border-tertiary: #58585a;
  --border-focus: rgba(10, 132, 255, 0.3);
  
  --shadow-small: 0 2px 8px rgba(0, 0, 0, 0.2);
  --shadow-medium: 0 10px 40px rgba(0, 0, 0, 0.3);
  --shadow-large: 0 20px 40px rgba(0, 0, 0, 0.4);
  
  --scrollbar-track: #2c2c2e;
  --scrollbar-thumb: #48484a;
  --scrollbar-thumb-hover: #58585a;
  --scrollbar-thumb-active: #68686a;
  
  --message-success-bg: #1f2f1f;
  --message-success-border: #2d4a2d;
  --message-error-bg: #2f1f1f;
  --message-error-border: #4a2d2d;
  
  --selection-bg: rgba(10, 132, 255, 0.3);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
  font-size: 14px;
  overflow: hidden;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* 改善滚动体验 */
.scrollable {
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

.scrollable::-webkit-scrollbar {
  width: 8px;
}

.scrollable::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 4px;
}

.scrollable::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.scrollable::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

.scrollable::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active);
}

/* 简约按钮样式 */
.modern-button {
  background: var(--bg-accent);
  color: var(--text-inverse);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  user-select: none;
}

.modern-button:hover {
  background: var(--bg-accent-hover);
  transform: translateY(-1px);
}

.modern-button:active {
  transform: translateY(0);
}

.modern-button:disabled {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  cursor: not-allowed;
  transform: none;
}

/* 按钮变体 */
.modern-button.secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.modern-button.secondary:hover {
  background: var(--bg-quaternary);
}

.modern-button.success {
  background: var(--bg-success);
}

.modern-button.success:hover {
  background: var(--bg-success-hover);
}

.modern-button.danger {
  background: var(--bg-danger);
}

.modern-button.danger:hover {
  background: var(--bg-danger-hover);
}

/* 简约卡片样式 */
.modern-card {
  background: var(--bg-primary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  transition: all 0.2s ease;
}

.modern-card:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-small);
}

/* 简约输入框 */
.modern-input {
  background: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 14px;
  color: var(--text-primary);
  transition: all 0.2s ease;
  outline: none;
  font-family: inherit;
}

.modern-input:focus {
  border-color: var(--text-accent);
  box-shadow: 0 0 0 3px var(--border-focus);
}

/* 简约复选框 */
.modern-checkbox {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid var(--border-secondary);
  border-radius: 4px;
  background: var(--bg-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.modern-checkbox:checked {
  background: var(--bg-accent);
  border-color: var(--bg-accent);
}

.modern-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-inverse);
  font-size: 11px;
  font-weight: 600;
}

/* 简约进度条 */
.modern-progress {
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
  height: 4px;
}

.modern-progress-bar {
  background: var(--bg-accent);
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* 简约表格样式 */
.modern-table {
  background: var(--bg-primary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modern-table-header {
  background: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-secondary);
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-primary);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.modern-table-body {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.modern-table-row {
  padding: 12px 16px;
  border-bottom: 1px solid var(--bg-tertiary);
  transition: background-color 0.15s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  min-height: 48px;
}

.modern-table-row:hover {
  background: var(--bg-secondary);
}

.modern-table-row.selected {
  background: var(--border-focus);
}

.modern-table-row:last-child {
  border-bottom: none;
}

/* 简约侧边栏 */
.modern-sidebar {
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
}

.modern-sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.modern-sidebar-item {
  padding: 10px 16px;
  margin: 2px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.15s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-primary);
  user-select: none;
}

.modern-sidebar-item:hover {
  background: var(--bg-quaternary);
}

.modern-sidebar-item.active {
  background: var(--bg-quaternary);
  color: var(--text-accent);
  border: 1px solid var(--text-accent);
}

/* 简约头部 */
.modern-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  flex-shrink: 0;
}

/* 简约模态框 */
.modern-modal {
  background: var(--bg-modal);
  animation: fadeIn 0.2s ease;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modern-modal-content {
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-medium);
  animation: slideUp 0.3s ease;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: scale(0.95);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* 简约工具栏 */
.modern-toolbar {
  background: var(--bg-primary);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  border: 1px solid var(--border-primary);
  flex-shrink: 0;
}

/* 简约状态栏 */
.modern-status-bar {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  padding: 8px 16px;
  font-size: 12px;
  color: var(--text-secondary);
  flex-shrink: 0;
}

/* 简约加载动画 */
.modern-loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-primary);
  border-radius: 50%;
  border-top-color: var(--bg-accent);
  animation: spin 1s linear infinite;
}

/* 信息横幅样式 */
.modern-info-banner {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid var(--border-secondary);
  background: var(--bg-tertiary);
}

.modern-info-banner.accent {
  background: var(--bg-secondary);
  border-color: var(--border-tertiary);
}

.modern-info-banner.accent .banner-title {
  color: var(--text-primary);
}

.modern-info-banner.accent .banner-text {
  color: var(--text-secondary);
}

.modern-info-banner .banner-title {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.modern-info-banner .banner-text {
  font-size: 12px;
  color: var(--text-secondary);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 简约文件图标 */
.modern-file-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 14px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  flex-shrink: 0;
}

/* 消息样式 */
.message-success {
  background: var(--message-success-bg);
  border: 1px solid var(--message-success-border);
  color: var(--text-success);
  border-radius: 8px;
}

.message-error {
  background: var(--message-error-bg);
  border: 1px solid var(--message-error-border);
  color: var(--text-danger);
  border-radius: 8px;
}

/* 文件名样式 */
.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

/* 路径导航样式 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.breadcrumb-item {
  cursor: pointer;
  color: var(--text-accent);
  font-weight: 500;
  white-space: nowrap;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: var(--text-accent-hover);
}

.breadcrumb-item.current {
  color: var(--text-primary);
  cursor: default;
}

.breadcrumb-separator {
  color: var(--text-secondary);
  margin: 0 4px;
  user-select: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-button {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .modern-sidebar-item {
    padding: 8px 12px;
  }
  
  .modern-table-row {
    padding: 8px 12px;
    min-height: 44px;
  }
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

::-webkit-scrollbar-corner {
  background: var(--scrollbar-track);
}

/* 选择文本样式 */
::selection {
  background: var(--selection-bg);
  color: var(--text-primary);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--bg-accent);
  outline-offset: 2px;
}

/* 图标动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
} 