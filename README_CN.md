# OD-Commander - OneDrive 第三方客户端

一个现代化、功能强大的 Windows OneDrive 客户端，基于 Electron 架构构建，提供高级文件管理和自定义同步功能。

## 🌟 主要特性

- **现代化界面**: 采用 Windows Fluent Design 设计语言
- **安全认证**: 使用 Microsoft OAuth 2.0 设备代码流进行安全认证
- **文件管理**: 完整的文件浏览、上传、下载、删除功能
- **搜索功能**: 快速搜索您的 OneDrive 文件
- **文件夹管理**: 创建和管理文件夹结构
- **用户账户**: 完整的用户信息和账户管理
- **响应式设计**: 适配不同屏幕尺寸的现代化界面

## 🚀 快速开始

### 前置要求

- Node.js 18.x 或更高版本
- npm 或 yarn 包管理器
- Windows 10/11 操作系统

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd OD-Commander
   ```

2. **安装依赖**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **配置 Microsoft 应用注册**
   
   在使用应用程序之前，您需要在 Microsoft Azure 门户中注册应用程序：

   a. 访问 [Azure 门户](https://portal.azure.com/)
   
   b. 导航到 "Azure Active Directory" > "应用注册"
   
   c. 点击 "新注册"
   
   d. 填写以下信息：
      - **名称**: OD-Commander
      - **支持的账户类型**: 任何组织目录中的账户和个人 Microsoft 账户
      - **重定向 URI**: 选择 "公共客户端/本机(移动和桌面)" 并输入 `http://localhost`
   
   e. 注册后，复制 "应用程序(客户端) ID"
   
   f. 在 "API 权限" 部分，添加以下权限：
      - Microsoft Graph > 委托权限 > Files.ReadWrite
      - Microsoft Graph > 委托权限 > User.Read
   
   g. 点击 "授予管理员同意"

4. **配置应用程序**
   
   打开 `src/main/config.ts` 文件，将您的应用程序 ID 替换到相应位置：
   ```typescript
   export const CLIENT_ID = '您的应用程序ID';
   ```

5. **启动开发服务器**
   ```bash
   npm run dev
   ```

## 📖 使用指南

### 首次使用

1. 启动应用程序后，您将看到欢迎界面
2. 点击 "连接到 OneDrive" 按钮
3. 将出现设备代码对话框，显示用户代码和验证 URL
4. 在浏览器中访问显示的 URL 并输入用户代码
5. 使用您的 Microsoft 账户登录并授权应用程序
6. 认证成功后，您将能够访问您的 OneDrive 文件

### 主要功能

#### 文件浏览
- 在左侧导航栏点击 "我的文件" 查看文件列表
- 双击文件夹进入子目录
- 使用面包屑导航快速返回上级目录

#### 文件操作
- **上传**: 点击上传按钮选择本地文件
- **下载**: 右键点击文件选择下载
- **删除**: 选择文件后点击删除按钮
- **重命名**: 右键点击文件选择重命名

#### 文件夹管理
- 点击 "新建文件夹" 按钮创建新文件夹
- 拖拽文件到文件夹中进行移动

#### 搜索功能
- 使用顶部搜索栏快速查找文件
- 支持文件名和内容搜索

### 账户管理

在左侧导航栏点击 "账户设置" 可以：
- 查看当前登录的用户信息
- 管理应用程序设置
- 退出登录

## 🛠️ 开发

### 项目结构

```
OD-Commander/
├── src/
│   ├── main/                 # Electron 主进程
│   │   ├── index.ts         # 主进程入口
│   │   ├── config.ts        # 配置文件
│   │   └── services/        # 服务层
│   │       ├── authService.ts    # 认证服务
│   │       └── graphService.ts   # Microsoft Graph API 服务
│   ├── preload/             # 预加载脚本
│   │   ├── index.ts         # 预加载脚本
│   │   └── index.d.ts       # 类型定义
│   └── renderer/            # 渲染进程 (React 应用)
│       ├── App.tsx          # 主应用组件
│       ├── main.tsx         # React 入口
│       ├── index.html       # HTML 模板
│       ├── stores/          # 状态管理
│       ├── components/      # React 组件
│       └── utils/           # 工具函数
├── resources/               # 应用资源
├── out/                     # 构建输出
└── dist/                    # 打包输出
```

### 可用脚本

- `npm run dev` - 启动开发服务器
- `npm run build` - 构建应用程序
- `npm run preview` - 预览构建结果
- `npm run pack` - 打包应用程序

### 技术栈

- **Electron**: 跨平台桌面应用框架
- **React**: 用户界面库
- **TypeScript**: 类型安全的 JavaScript
- **Vite**: 快速构建工具
- **Zustand**: 轻量级状态管理
- **Microsoft Graph SDK**: Microsoft Graph API 客户端
- **MSAL Node**: Microsoft 认证库

## 🔧 配置选项

### 认证配置

在 `src/main/config.ts` 中可以配置：

```typescript
export const CLIENT_ID = '您的应用程序ID';
export const AUTHORITY = 'https://login.microsoftonline.com/common';
export const SCOPES = ['https://graph.microsoft.com/Files.ReadWrite', 'https://graph.microsoft.com/User.Read'];
```

### 应用程序设置

- **窗口大小**: 在 `src/main/index.ts` 中修改窗口尺寸
- **主题**: 在 `src/renderer/main.tsx` 中切换 Fluent UI 主题
- **语言**: 界面已本地化为中文

## 🐛 故障排除

### 常见问题

1. **白屏问题**
   - 确保所有依赖都已正确安装
   - 检查控制台是否有 JavaScript 错误
   - 尝试清除缓存并重新启动

2. **认证失败**
   - 验证 Microsoft 应用注册配置
   - 确保客户端 ID 正确
   - 检查网络连接

3. **文件操作失败**
   - 确认用户已正确认证
   - 检查 Microsoft Graph API 权限
   - 验证网络连接

### 调试

启用开发者工具进行调试：
```bash
# 在开发模式下，按 F12 打开开发者工具
# 或在代码中添加：
webContents.openDevTools()
```

## 📝 更新日志

### v1.0.0 (当前版本)
- 初始版本发布
- 基本文件管理功能
- Microsoft OAuth 认证
- 现代化用户界面
- 中文本地化

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- Microsoft Graph API 团队
- Electron 社区
- React 和 TypeScript 社区
- 所有贡献者和测试用户

## 📞 支持

如果您遇到问题或有建议，请：

1. 查看 [故障排除](#故障排除) 部分
2. 搜索现有的 [Issues](../../issues)
3. 创建新的 Issue 描述您的问题

---

**注意**: 这是一个第三方应用程序，与 Microsoft 公司无关。请确保遵守 Microsoft 的服务条款和使用政策。 