import { Client } from '@microsoft/microsoft-graph-client'
import { AuthService } from './authService'

export interface DriveItem {
  id: string
  name: string
  size?: number
  lastModifiedDateTime: string
  folder?: any
  file?: any
  webUrl: string
  downloadUrl?: string
}

export interface UserInfo {
  id: string
  displayName: string
  mail: string
  userPrincipalName: string
}

export class GraphService {
  private client: Client | null = null
  private authService: AuthService

  constructor(authService: AuthService) {
    this.authService = authService
  }

  private async getClient(): Promise<Client> {
    if (!this.client) {
      const accessToken = await this.authService.getAccessToken()
      if (!accessToken) {
        throw new Error('No access token available')
      }

      this.client = Client.init({
        authProvider: async (done) => {
          try {
            const token = await this.authService.getAccessToken()
            done(null, token)
          } catch (error) {
            done(error, null)
          }
        }
      })
    }
    return this.client
  }

  async getUserInfo(): Promise<UserInfo> {
    try {
      const client = await this.getClient()
      const user = await client.api('/me').get()
      
      return {
        id: user.id,
        displayName: user.displayName,
        mail: user.mail || user.userPrincipalName,
        userPrincipalName: user.userPrincipalName
      }
    } catch (error) {
      console.error('Failed to get user info:', error)
      throw error
    }
  }

  async getDriveItems(path: string = ''): Promise<DriveItem[]> {
    try {
      const client = await this.getClient()
      let apiPath = '/me/drive/root/children'
      
      if (path && path !== '/') {
        // Remove leading slash if present
        const cleanPath = path.startsWith('/') ? path.substring(1) : path
        apiPath = `/me/drive/root:/${cleanPath}:/children`
      }

      const response = await client.api(apiPath).get()
      
      return response.value.map((item: any) => ({
        id: item.id,
        name: item.name,
        size: item.size,
        lastModifiedDateTime: item.lastModifiedDateTime,
        folder: item.folder,
        file: item.file,
        webUrl: item.webUrl,
        downloadUrl: item['@microsoft.graph.downloadUrl']
      }))
    } catch (error) {
      console.error('Failed to get drive items:', error)
      throw error
    }
  }

  async createFolder(name: string, parentPath: string = ''): Promise<DriveItem> {
    try {
      const client = await this.getClient()
      let apiPath = '/me/drive/root/children'
      
      if (parentPath && parentPath !== '/') {
        const cleanPath = parentPath.startsWith('/') ? parentPath.substring(1) : parentPath
        apiPath = `/me/drive/root:/${cleanPath}:/children`
      }

      const folderData = {
        name: name,
        folder: {},
        '@microsoft.graph.conflictBehavior': 'rename'
      }

      const response = await client.api(apiPath).post(folderData)
      
      return {
        id: response.id,
        name: response.name,
        size: response.size,
        lastModifiedDateTime: response.lastModifiedDateTime,
        folder: response.folder,
        file: response.file,
        webUrl: response.webUrl,
        downloadUrl: response['@microsoft.graph.downloadUrl']
      }
    } catch (error) {
      console.error('Failed to create folder:', error)
      throw error
    }
  }

  async uploadFile(filePath: string, fileName: string, parentPath: string = ''): Promise<DriveItem> {
    try {
      const client = await this.getClient()
      const fs = require('fs')
      
      let apiPath = `/me/drive/root:/${fileName}:/content`
      if (parentPath && parentPath !== '/') {
        const cleanPath = parentPath.startsWith('/') ? parentPath.substring(1) : parentPath
        apiPath = `/me/drive/root:/${cleanPath}/${fileName}:/content`
      }

      const fileContent = fs.readFileSync(filePath)
      const response = await client.api(apiPath).put(fileContent)
      
      return {
        id: response.id,
        name: response.name,
        size: response.size,
        lastModifiedDateTime: response.lastModifiedDateTime,
        folder: response.folder,
        file: response.file,
        webUrl: response.webUrl,
        downloadUrl: response['@microsoft.graph.downloadUrl']
      }
    } catch (error) {
      console.error('Failed to upload file:', error)
      throw error
    }
  }

  async uploadFileBuffer(fileBuffer: Buffer, fileName: string, parentPath: string = ''): Promise<DriveItem> {
    try {
      const client = await this.getClient()
      
      let apiPath = `/me/drive/root:/${fileName}:/content`
      if (parentPath && parentPath !== '/') {
        const cleanPath = parentPath.startsWith('/') ? parentPath.substring(1) : parentPath
        apiPath = `/me/drive/root:/${cleanPath}/${fileName}:/content`
      }

      const response = await client.api(apiPath).put(fileBuffer)
      
      return {
        id: response.id,
        name: response.name,
        size: response.size,
        lastModifiedDateTime: response.lastModifiedDateTime,
        folder: response.folder,
        file: response.file,
        webUrl: response.webUrl,
        downloadUrl: response['@microsoft.graph.downloadUrl']
      }
    } catch (error) {
      console.error('Failed to upload file buffer:', error)
      throw error
    }
  }

  async downloadFile(itemId: string, localPath: string): Promise<void> {
    try {
      const client = await this.getClient()
      const fs = require('fs')
      
      const response = await client.api(`/me/drive/items/${itemId}/content`).getStream()
      const writeStream = fs.createWriteStream(localPath)
      
      return new Promise((resolve, reject) => {
        response.pipe(writeStream)
        writeStream.on('finish', resolve)
        writeStream.on('error', reject)
      })
    } catch (error) {
      console.error('Failed to download file:', error)
      throw error
    }
  }

  async deleteItem(itemId: string): Promise<void> {
    try {
      const client = await this.getClient()
      await client.api(`/me/drive/items/${itemId}`).delete()
    } catch (error) {
      console.error('Failed to delete item:', error)
      throw error
    }
  }

  async searchFiles(query: string): Promise<DriveItem[]> {
    try {
      const client = await this.getClient()
      const response = await client.api(`/me/drive/root/search(q='${query}')`).get()
      
      return response.value.map((item: any) => ({
        id: item.id,
        name: item.name,
        size: item.size,
        lastModifiedDateTime: item.lastModifiedDateTime,
        folder: item.folder,
        file: item.file,
        webUrl: item.webUrl,
        downloadUrl: item['@microsoft.graph.downloadUrl']
      }))
    } catch (error) {
      console.error('Failed to search files:', error)
      throw error
    }
  }

  // 获取用户存储信息
  async getStorageInfo(): Promise<{
    quota: {
      total: number
      used: number
      remaining: number
      deleted: number
      state: string
    }
    driveType?: string
  }> {
    try {
      const client = await this.getClient()
      
      // 首先尝试获取个人OneDrive
      let response
      let driveType = 'personal'
      
      try {
        response = await client.api('/me/drive').get()
      } catch (personalError) {
        console.log('Personal OneDrive not available, trying SharePoint...')
        
        // 如果个人OneDrive不可用，尝试获取默认SharePoint站点
        try {
          const sitesResponse = await client.api('/me/followedSites').get()
          if (sitesResponse.value && sitesResponse.value.length > 0) {
            // 使用第一个可用的SharePoint站点
            const siteId = sitesResponse.value[0].id
            response = await client.api(`/sites/${siteId}/drive`).get()
            driveType = 'sharepoint'
          } else {
            // 尝试获取默认文档库
            response = await client.api('/sites/root/drive').get()
            driveType = 'sharepoint'
          }
        } catch (sharepointError) {
          console.log('SharePoint drive not available, trying alternative methods...')
          
          // 最后尝试获取用户的默认驱动器
          try {
            const drivesResponse = await client.api('/me/drives').get()
            if (drivesResponse.value && drivesResponse.value.length > 0) {
              response = drivesResponse.value[0]
              driveType = response.driveType || 'unknown'
            } else {
              throw new Error('No drives available')
            }
          } catch (drivesError) {
            // 如果所有方法都失败，返回默认值
            console.warn('All storage info methods failed, returning default values')
            return {
              quota: {
                total: 0,
                used: 0,
                remaining: 0,
                deleted: 0,
                state: 'unavailable'
              },
              driveType: 'unavailable'
            }
          }
        }
      }
      
      // 处理SharePoint特殊情况
      if (response && !response.quota && driveType === 'sharepoint') {
        // SharePoint可能没有quota信息，尝试其他方式获取
        try {
          const rootResponse = await client.api(`/drives/${response.id}/root`).get()
          const quota = {
            total: rootResponse.quota?.total || 0,
            used: rootResponse.quota?.used || 0,
            remaining: rootResponse.quota?.remaining || 0,
            deleted: rootResponse.quota?.deleted || 0,
            state: rootResponse.quota?.state || 'normal'
          }
          
          return { quota, driveType }
        } catch (rootError) {
          console.log('Could not get quota from root, using default values')
        }
      }
      
      return {
        quota: response?.quota || {
          total: 0,
          used: 0,
          remaining: 0,
          deleted: 0,
          state: response ? 'normal' : 'unavailable'
        },
        driveType
      }
    } catch (error) {
      console.error('Failed to get storage info:', error)
      
      // 返回默认值而不是抛出错误
      return {
        quota: {
          total: 0,
          used: 0,
          remaining: 0,
          deleted: 0,
          state: 'error'
        },
        driveType: 'error'
      }
    }
  }

  // 获取用户详细信息（包括头像）
  async getUserProfile(): Promise<{
    id: string
    displayName: string
    userPrincipalName: string
    mail: string
    jobTitle?: string
    officeLocation?: string
    businessPhones: string[]
    mobilePhone?: string
    photo?: string
  }> {
    try {
      const client = await this.getClient()
      
      // 获取基本用户信息
      const userData = await client.api('/me').get()

      // 尝试获取用户头像
      let photoUrl = undefined
      try {
        // 首先检查用户是否有头像
        const photoMetadata = await client.api('/me/photo').get()
        if (photoMetadata && photoMetadata.id) {
          // 如果有头像元数据，则获取实际图片
          const photoResponse = await client.api('/me/photo/$value').get()
          if (photoResponse) {
            // 处理不同类型的响应
            let photoBuffer: Buffer
            
            if (photoResponse instanceof Buffer) {
              photoBuffer = photoResponse
            } else if (photoResponse instanceof ArrayBuffer) {
              photoBuffer = Buffer.from(photoResponse)
            } else if (typeof photoResponse === 'object' && photoResponse.arrayBuffer) {
              // 处理Blob对象
              const arrayBuffer = await photoResponse.arrayBuffer()
              photoBuffer = Buffer.from(arrayBuffer)
            } else {
              console.log('getUserProfile: 不支持的头像数据类型:', typeof photoResponse)
              throw new Error('不支持的头像数据类型')
            }
            const isJpeg = photoBuffer[0] === 0xFF && photoBuffer[1] === 0xD8
            const isPng = photoBuffer[0] === 0x89 && photoBuffer[1] === 0x50
            
            let mimeType = 'image/jpeg' // 默认
            if (isPng) {
              mimeType = 'image/png'
            } else if (isJpeg) {
              mimeType = 'image/jpeg'
            }
            
            photoUrl = `data:${mimeType};base64,${photoBuffer.toString('base64')}`
            console.log('用户头像获取成功，大小:', photoBuffer.length, '字节')
          }
        }
      } catch (photoError) {
        console.log('No user photo available or failed to fetch photo:', photoError.message)
        
        // 尝试备用方法获取头像
        try {
          const photoResponse = await client.api('/me/photo/$value').get()
          if (photoResponse) {
            // 处理不同类型的响应
            let photoBuffer: Buffer
            
            if (photoResponse instanceof Buffer) {
              photoBuffer = photoResponse
            } else if (photoResponse instanceof ArrayBuffer) {
              photoBuffer = Buffer.from(photoResponse)
            } else if (typeof photoResponse === 'object' && photoResponse.arrayBuffer) {
              // 处理Blob对象
              const arrayBuffer = await photoResponse.arrayBuffer()
              photoBuffer = Buffer.from(arrayBuffer)
            } else {
              console.log('备用方法: 不支持的头像数据类型:', typeof photoResponse)
              throw new Error('不支持的头像数据类型')
            }
            photoUrl = `data:image/jpeg;base64,${photoBuffer.toString('base64')}`
            console.log('通过备用方法获取用户头像成功')
          }
        } catch (fallbackError) {
          console.log('备用头像获取方法也失败:', fallbackError.message)
        }
      }

      return {
        id: userData.id,
        displayName: userData.displayName,
        userPrincipalName: userData.userPrincipalName,
        mail: userData.mail,
        jobTitle: userData.jobTitle,
        officeLocation: userData.officeLocation,
        businessPhones: userData.businessPhones || [],
        mobilePhone: userData.mobilePhone,
        photo: photoUrl
      }
    } catch (error) {
      console.error('Failed to get user profile:', error)
      throw error
    }
  }

  // 专门获取用户头像的方法
  async getUserPhoto(size: '48x48' | '64x64' | '96x96' | '120x120' | '240x240' | '360x360' | '432x432' | '504x504' | '648x648' = '120x120'): Promise<string | null> {
    try {
      const client = await this.getClient()
      
      console.log(`尝试获取用户头像，尺寸: ${size}`)
      
      // 方法1: 尝试获取指定尺寸的头像
      const methods = [
        () => client.api(`/me/photos/${size}/$value`).get(),
        () => client.api('/me/photo/$value').get(),
        () => client.api('/me/photos/120x120/$value').get(),
        () => client.api('/me/photos/96x96/$value').get(),
        () => client.api('/me/photos/64x64/$value').get(),
        () => client.api('/me/photos/48x48/$value').get()
      ]
      
      for (let i = 0; i < methods.length; i++) {
        try {
          console.log(`尝试方法 ${i + 1}...`)
          const photoResponse = await methods[i]()
           
          if (photoResponse) {
            // 处理不同类型的响应
            let photoBuffer: Buffer
             
            if (photoResponse instanceof Buffer) {
              photoBuffer = photoResponse
            } else if (photoResponse instanceof ArrayBuffer) {
              photoBuffer = Buffer.from(photoResponse)
            } else if (typeof photoResponse === 'object' && photoResponse.arrayBuffer) {
              // 处理Blob对象
              const arrayBuffer = await photoResponse.arrayBuffer()
              photoBuffer = Buffer.from(arrayBuffer)
            } else {
              console.log(`方法 ${i + 1} 返回了不支持的数据类型:`, typeof photoResponse)
              continue
            }
             
            if (photoBuffer.length > 0) {
              // 检测图片格式
              const isJpeg = photoBuffer[0] === 0xFF && photoBuffer[1] === 0xD8
              const isPng = photoBuffer[0] === 0x89 && photoBuffer[1] === 0x50
              const isGif = photoBuffer[0] === 0x47 && photoBuffer[1] === 0x49
              
              let mimeType = 'image/jpeg' // 默认
              if (isPng) {
                mimeType = 'image/png'
              } else if (isJpeg) {
                mimeType = 'image/jpeg'
              } else if (isGif) {
                mimeType = 'image/gif'
              }
              
              const photoUrl = `data:${mimeType};base64,${photoBuffer.toString('base64')}`
              console.log(`用户头像获取成功 (方法${i + 1})，大小:`, photoBuffer.length, '字节，格式:', mimeType)
              return photoUrl
            }
          }
        } catch (methodError) {
          console.log(`方法 ${i + 1} 失败:`, methodError.message)
        }
      }
      
      console.log('所有头像获取方法都失败了')
      return null
    } catch (error) {
      console.error('获取用户头像时发生错误:', error)
      return null
    }
  }

} 