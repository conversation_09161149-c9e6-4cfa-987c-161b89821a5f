import { create } from 'zustand'

interface UserInfo {
  id: string
  name: string
  email: string
}

interface AppState {
  // Authentication state
  isAuthenticated: boolean
  accessToken: string | null
  userInfo: UserInfo | null

  // UI state
  currentView: 'welcome' | 'files' | 'sync' | 'settings'
  sidebarCollapsed: boolean
  theme: 'light' | 'dark' | 'system'

  // File management state
  currentPath: string
  files: any[]
  selectedFiles: string[]
  isLoading: boolean

  // Actions
  setAuthenticated: (token: string, userInfo: { id: string; displayName: string; mail: string; userPrincipalName: string }) => void
  logout: () => void
  setCurrentView: (view: AppState['currentView']) => void
  toggleSidebar: () => void
  setTheme: (theme: AppState['theme']) => void
  setCurrentPath: (path: string) => void
  setFiles: (files: any[]) => void
  setSelectedFiles: (files: string[]) => void
  setLoading: (loading: boolean) => void
}

export const useAppStore = create<AppState>((set) => ({
  // Initial state
  isAuthenticated: false,
  accessToken: null,
  userInfo: null,
  
  currentView: 'welcome',
  sidebarCollapsed: false,
  theme: 'light',
  
  currentPath: '/',
  files: [],
  selectedFiles: [],
  isLoading: false,

  // Actions
  setAuthenticated: (token: string, userInfo: { id: string; displayName: string; mail: string; userPrincipalName: string }) => 
    set({ 
      isAuthenticated: true, 
      accessToken: token, 
      userInfo: {
        id: userInfo.id,
        name: userInfo.displayName,
        email: userInfo.mail || userInfo.userPrincipalName
      },
      currentView: 'files' 
    }),
    
  logout: () => 
    set({ 
      isAuthenticated: false, 
      accessToken: null, 
      userInfo: null,
      currentView: 'welcome',
      files: [],
      selectedFiles: [],
      currentPath: '/'
    }),
    
  setCurrentView: (view) => set({ currentView: view }),
  
  toggleSidebar: () => set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),
  
  setTheme: (theme) => set({ theme }),
  
  setCurrentPath: (path) => set({ currentPath: path }),
  
  setFiles: (files) => set({ files }),
  
  setSelectedFiles: (files) => set({ selectedFiles: files }),
  
  setLoading: (loading) => set({ isLoading: loading })
})) 