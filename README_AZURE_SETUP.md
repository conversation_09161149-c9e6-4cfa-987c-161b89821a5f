# OneDrive Commander - Azure 应用注册配置指南

## 🔧 重要说明

OneDrive Commander 使用 **1Panel 风格的 Authorization Code Flow**，需要您在 Azure Portal 创建自己的应用注册。

## 📁 配置文件位置

配置文件保存在用户数据目录：
- **Windows**: `C:\Users\<USER>\AppData\Roaming\od-commander\config.json`
- **macOS**: `~/Library/Application Support/od-commander/config.json`
- **Linux**: `~/.config/od-commander/config.json`

这确保：

- ✅ **真正的90天refresh token有效期**
- ✅ **企业级安全性和合规性**  
- ✅ **自动后台token刷新**
- ✅ **无需频繁重新登录**

---

## 📋 步骤1：创建Azure应用注册

### 1.1 访问Azure Portal
访问并登录 Microsoft Azure：
```
https://portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationsListBlade
```

### 1.2 新建应用注册
1. 点击 **"新注册"** 按钮
2. 填写应用信息：
   - **名称**：`OneDrive Commander` (或您喜欢的名称)
   - **支持的账户类型**：选择 `任何组织目录(任何 Azure AD 目录 - 多租户)中的账户和个人 Microsoft 账户`
   - **重定向URI**：
     - 类型：选择 `Web`
     - URI：输入 `http://localhost:3000/auth/callback`
3. 点击 **"注册"**

---

## 📋 步骤2：获取客户端ID

1. 在应用主页，复制 **"应用程序(客户端) ID"**
2. 记录此ID，稍后需要配置到应用中

**示例格式**：`12345678-1234-1234-1234-123456789012`

---

## 📋 步骤3：创建客户端密码

### 3.1 创建密码
1. 在左侧菜单选择 **"证书和密码"**
2. 点击 **"新客户端密码"**
3. 填写信息：
   - **描述**：`OneDrive Commander Secret`
   - **过期时间**：建议选择 `24个月`
4. 点击 **"添加"**

### 3.2 保存密码值
⚠️ **重要**：生成的 **"值"** 只显示一次，请立即复制并保存！

**示例格式**：`xzg8Q~abcdefghijklmnopqrstuvwxyz123456789`

---

## 📋 步骤4：配置API权限

### 4.1 添加Microsoft Graph权限
1. 在左侧菜单选择 **"API权限"**
2. 点击 **"添加权限"**
3. 选择 **"Microsoft Graph"**
4. 选择 **"委托的权限"**
5. 勾选以下权限：
   - ✅ `Files.ReadWrite.All` (读写用户文件)
   - ✅ `offline_access` (离线访问，获取90天refresh token)
   - ✅ `User.Read` (读取用户基本信息)
6. 点击 **"添加权限"**

### 4.2 授予管理员同意
点击 **"为 [租户] 授予管理员同意"** (如果您是管理员)

---

## 📋 步骤5：配置身份验证

### 5.1 验证重定向URI
1. 在左侧菜单选择 **"身份验证"**
2. 确认 **重定向URI** 包含：`http://localhost:3000/auth/callback`

### 5.2 启用隐式授权
在 **"隐式授权和混合流"** 下启用：
- ✅ **访问令牌**
- ✅ **ID 令牌**

---

## 📋 步骤6：配置应用程序

### 6.1 使用应用内配置界面
首次启动应用时，会自动显示Azure配置界面，按提示填入信息即可。

### 6.2 手动编辑配置文件（可选）
您也可以直接编辑配置文件：`{用户数据目录}/config.json`

```json
{
  "auth": {
    "clientId": "12345678-1234-1234-1234-123456789012",
    "clientSecret": "xzg8Q~abcdefghijklmnopqrstuvwxyz123456789",
    "authority": "https://login.microsoftonline.com/common",
    "redirectUri": "http://localhost:3000/auth/callback"
  }
}
```

### 6.3 重启应用
配置保存后，重启 OneDrive Commander 应用。

---

## 🚀 使用流程

### 登录流程
1. 启动应用，点击登录
2. 应用会弹出授权说明窗口
3. 点击 **"打开授权页面"**
4. 在浏览器中完成Microsoft登录
5. 授权后，复制重定向URL中的授权码
6. 返回应用，粘贴授权码
7. 完成！享受90天免登录体验

### 授权码提取说明
重定向后的URL类似：
```
http://localhost:3000/auth/callback?code=M.C107_BAY...很长的字符串...&state=xxxx
```

只需复制 `code=` 后面到 `&` 前面的部分，或者直接粘贴整个URL，应用会自动提取。

---

## 🔧 故障排除

### 常见问题

**Q: 提示"未配置Azure应用注册信息"**
A: 请确保在 `config.ts` 中正确填写了 `clientId` 和 `clientSecret`

**Q: 授权时提示重定向URI不匹配**
A: 请确认Azure应用注册中的重定向URI为 `http://localhost:3000/auth/callback`

**Q: 权限不足**
A: 请确认已添加必要的API权限并授予了管理员同意

**Q: 客户端密码过期**
A: 在Azure Portal重新创建客户端密码，并更新配置文件

### 安全提醒
- 🔒 **不要将客户端密码提交到版本控制系统**
- 🔒 **定期更换客户端密码**
- 🔒 **仅授予必要的最小权限**

---

## 🎯 完成后的优势

- ✅ **真正的90天refresh token有效期**
- ✅ **安全的Authorization Code Flow with PKCE**
- ✅ **自动后台token刷新**
- ✅ **企业级安全性和合规性**
- ✅ **无需频繁重新登录**
- ✅ **符合Microsoft最佳实践**

---

## 📞 技术支持

如果在配置过程中遇到问题，请：
1. 检查Azure Portal中的配置是否正确
2. 验证 `config.ts` 中的参数是否准确
3. 查看应用控制台输出的错误信息
4. 参考Microsoft官方文档：https://docs.microsoft.com/azure/active-directory/develop/

祝您使用愉快！🎉 