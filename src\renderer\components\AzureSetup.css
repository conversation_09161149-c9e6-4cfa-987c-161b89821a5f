.azure-setup-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

[data-theme="dark"] .azure-setup-container {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

.azure-setup-card {
  background: var(--bg-primary);
  border-radius: 16px;
  box-shadow: var(--shadow-large);
  max-width: 800px;
  width: 100%;
  padding: 32px;
  max-height: 90vh;
  overflow-y: auto;
}

.setup-header {
  text-align: center;
  margin-bottom: 32px;
}

.setup-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
}

.setup-subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.setup-benefits {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

[data-theme="dark"] .setup-benefits {
  background: var(--bg-secondary);
  border-color: var(--border-primary);
}

.setup-benefits h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  color: var(--text-accent);
}

.setup-benefits ul {
  margin: 0;
  padding-left: 20px;
  list-style: none;
}

.setup-benefits li {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary);
  position: relative;
}

.setup-guide {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

[data-theme="dark"] .setup-guide {
  background: var(--bg-secondary);
  border-color: var(--border-primary);
}

.setup-guide h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: var(--text-accent);
}

.setup-guide ol {
  margin: 0;
  padding-left: 20px;
}

.setup-guide li {
  margin-bottom: 12px;
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.link-button {
  background: none;
  border: none;
  color: var(--text-accent);
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
  margin-left: 8px;
  padding: 2px 4px;
}

.link-button:hover {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

.setup-guide code {
  background: var(--bg-quaternary);
  color: var(--text-primary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Cascadia Code', 'SF Mono', monospace;
  font-size: 13px;
}

.guide-link {
  margin-top: 16px;
  text-align: center;
}

.guide-link a {
  color: var(--text-accent);
  text-decoration: none;
  font-weight: 500;
}

.guide-link a:hover {
  text-decoration: underline;
}

.config-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

.required {
  color: var(--text-danger);
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-secondary);
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--text-accent);
  box-shadow: 0 0 0 3px var(--border-focus);
}

.form-input[type="password"] {
  font-family: 'Cascadia Code', 'SF Mono', monospace;
}

.form-help {
  display: block;
  margin-top: 6px;
  font-size: 12px;
  color: var(--text-tertiary);
  line-height: 1.4;
}

.error-message {
  background: var(--message-error-bg);
  border: 1px solid var(--message-error-border);
  color: var(--text-danger);
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.button.primary {
  background: var(--bg-accent);
  color: var(--text-inverse);
}

.button.primary:hover:not(:disabled) {
  background: var(--bg-accent-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.button.secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-secondary);
}

.button.secondary:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--border-tertiary);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.config-file-info {
  text-align: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--border-primary);
}

.config-file-info small {
  color: var(--text-tertiary);
  line-height: 1.5;
}

.config-file-info code {
  background: var(--bg-quaternary);
  color: var(--text-primary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Cascadia Code', 'SF Mono', monospace;
  font-size: 12px;
}

/* 已配置状态的样式 */
.config-info {
  background: var(--message-success-bg);
  border: 1px solid var(--message-success-border);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.config-value {
  font-family: 'Cascadia Code', 'SF Mono', monospace;
  color: var(--text-success);
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .azure-setup-container {
    padding: 12px;
  }
  
  .azure-setup-card {
    padding: 20px;
  }
  
  .setup-header h2 {
    font-size: 24px;
  }
  
  .form-actions, .button-group {
    flex-direction: column;
  }
  
  .button {
    width: 100%;
  }
} 