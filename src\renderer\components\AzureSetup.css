.azure-setup-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.azure-setup-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  width: 100%;
  padding: 32px;
  max-height: 90vh;
  overflow-y: auto;
}

.setup-header {
  text-align: center;
  margin-bottom: 32px;
}

.setup-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #333;
}

.setup-subtitle {
  margin: 0;
  font-size: 16px;
  color: #666;
  line-height: 1.5;
}

.setup-benefits {
  background: #f8f9ff;
  border: 1px solid #e3e8ff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.setup-benefits h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  color: #4f46e5;
}

.setup-benefits ul {
  margin: 0;
  padding-left: 20px;
  list-style: none;
}

.setup-benefits li {
  margin-bottom: 8px;
  font-size: 14px;
  color: #555;
  position: relative;
}

.setup-guide {
  background: #fff8e1;
  border: 1px solid #ffcc02;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.setup-guide h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #f57c00;
}

.setup-guide ol {
  margin: 0;
  padding-left: 20px;
}

.setup-guide li {
  margin-bottom: 12px;
  font-size: 14px;
  color: #555;
  line-height: 1.5;
}

.link-button {
  background: none;
  border: none;
  color: #4f46e5;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
  margin-left: 8px;
  padding: 2px 4px;
}

.link-button:hover {
  background: #f0f0ff;
  border-radius: 4px;
}

.setup-guide code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Cascadia Code', 'SF Mono', monospace;
  font-size: 13px;
}

.guide-link {
  margin-top: 16px;
  text-align: center;
}

.guide-link a {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 500;
}

.guide-link a:hover {
  text-decoration: underline;
}

.config-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.required {
  color: #ef4444;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input[type="password"] {
  font-family: 'Cascadia Code', 'SF Mono', monospace;
}

.form-help {
  display: block;
  margin-top: 6px;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.button.primary {
  background: #4f46e5;
  color: white;
}

.button.primary:hover:not(:disabled) {
  background: #4338ca;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.button.secondary {
  background: #f9fafb;
  color: #374151;
  border: 1px solid #d1d5db;
}

.button.secondary:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.config-file-info {
  text-align: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.config-file-info small {
  color: #6b7280;
  line-height: 1.5;
}

.config-file-info code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Cascadia Code', 'SF Mono', monospace;
  font-size: 12px;
}

/* 已配置状态的样式 */
.config-info {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.config-value {
  font-family: 'Cascadia Code', 'SF Mono', monospace;
  color: #059669;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .azure-setup-container {
    padding: 12px;
  }
  
  .azure-setup-card {
    padding: 20px;
  }
  
  .setup-header h2 {
    font-size: 24px;
  }
  
  .form-actions, .button-group {
    flex-direction: column;
  }
  
  .button {
    width: 100%;
  }
} 