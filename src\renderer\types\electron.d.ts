export interface ElectronAPI {
  ping: () => Promise<string>
  
  auth: {
    login: () => Promise<{
      success: boolean
      accessToken?: string
      userInfo?: {
        id: string
        displayName: string
        name: string
        email: string
      }
      profile?: any
      error?: string
    }>
    logout: () => Promise<{
      success: boolean
      error?: string
    }>
    autoLogin: () => Promise<{
      success: boolean
      accessToken?: string
      userInfo?: any
      profile?: any
      isAutoLogin?: boolean
      error?: string
    }>
  }

  accounts: {
    getAll: () => Promise<{
      success: boolean
      accounts?: Array<{
        id: string
        displayName: string
        userPrincipalName: string
        mail: string
        jobTitle?: string
        officeLocation?: string
        businessPhones: string[]
        mobilePhone?: string
        photo?: string
        driveType?: string
        lastLoginTime: number
        isActive: boolean
      }>
      error?: string
    }>
    getActive: () => Promise<{
      success: boolean
      account?: any
      error?: string
    }>
    switch: (accountId: string) => Promise<{
      success: boolean
      account?: any
      accessToken?: string
      error?: string
    }>
    remove: (accountId: string) => Promise<{
      success: boolean
      error?: string
    }>
    clearAll: () => Promise<{
      success: boolean
      error?: string
    }>
  }
  
  files: {
    list: (path?: string) => Promise<{
      success: boolean
      items?: Array<{
        id: string
        name: string
        size?: number
        lastModifiedDateTime: string
        folder?: any
        file?: any
        webUrl: string
        downloadUrl?: string
      }>
      error?: string
    }>
    createFolder: (name: string, parentPath?: string) => Promise<{
      success: boolean
      folder?: any
      error?: string
    }>
    upload: (filePath: string, fileName: string, parentPath?: string) => Promise<{
      success: boolean
      file?: any
      error?: string
    }>
    uploadBuffer: (fileData: number[], fileName: string, parentPath?: string) => Promise<{
      success: boolean
      file?: any
      error?: string
    }>
    download: (itemId: string, localPath: string) => Promise<{
      success: boolean
      error?: string
    }>
    delete: (itemId: string) => Promise<{
      success: boolean
      error?: string
    }>
    search: (query: string) => Promise<{
      success: boolean
      items?: any[]
      error?: string
    }>
  }
  
  user: {
    getStorageInfo: () => Promise<{
      success: boolean
      storageInfo?: {
        quota: {
          total: number
          used: number
          remaining: number
          deleted: number
          state: string
        }
        driveType?: string
      }
      error?: string
    }>
    getProfile: () => Promise<{
      success: boolean
      profile?: {
        id: string
        displayName: string
        userPrincipalName: string
        mail: string
        jobTitle?: string
        officeLocation?: string
        businessPhones: string[]
        mobilePhone?: string
        photo?: string
      }
      error?: string
    }>
    getPhoto: (size?: string) => Promise<{
      success: boolean
      photo?: string | null
      error?: string
    }>
  }

  config: {
    get: () => Promise<any>
    update: (updates: any) => Promise<{ success: boolean; error?: string }>
    reset: () => Promise<{ success: boolean; error?: string }>
    getPath: () => Promise<string>
    setCustomPath: (customPath: string) => Promise<{ success: boolean; error?: string }>
    export: (exportPath: string) => Promise<{ success: boolean; error?: string }>
    import: (importPath: string) => Promise<{ success: boolean; error?: string }>
    clearCache: () => Promise<{ success: boolean; error?: string }>
  }

  showSaveDialog: (options: any) => Promise<{
    canceled: boolean
    filePath?: string
  }>
  showOpenDialog: (options: any) => Promise<{
    canceled: boolean
    filePaths?: string[]
  }>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
} 