import { Tray, Menu, nativeImage, BrowserWindow } from 'electron'
import { join } from 'path'

export class TrayManager {
  private tray: Tray | null = null
  private mainWindow: BrowserWindow | null = null

  constructor(mainWindow: BrowserWindow) {
    this.mainWindow = mainWindow
  }

  public createTray(): void {
    if (this.tray) {
      return // Tray already exists
    }

    // Create tray icon
    const iconPath = join(__dirname, '../../resources/icon.png')
    let trayIcon: Electron.NativeImage
    
    try {
      trayIcon = nativeImage.createFromPath(iconPath)
      if (trayIcon.isEmpty()) {
        console.warn('Icon file not found or empty, using fallback icon')
        trayIcon = this.createFallbackIcon()
      }
    } catch (error) {
      console.error('Failed to create tray icon:', error)
      trayIcon = this.createFallbackIcon()
    }
    
    // Resize icon for system tray (16x16 is standard for Windows)
    trayIcon = trayIcon.resize({ width: 16, height: 16 })
    
    this.tray = new Tray(trayIcon)
    this.tray.setToolTip('OD Commander - OneDrive 文件管理器')

    // Set context menu
    this.updateContextMenu()

    // Handle double click on tray icon
    this.tray.on('double-click', () => {
      this.toggleWindowVisibility()
    })

    // Handle single click on tray icon (Windows behavior)
    this.tray.on('click', () => {
      this.toggleWindowVisibility()
    })

    // Update context menu when window visibility changes
    if (this.mainWindow) {
      this.mainWindow.on('show', () => this.updateContextMenu())
      this.mainWindow.on('hide', () => this.updateContextMenu())
    }

    console.log('System tray created successfully')
  }

  public destroyTray(): void {
    if (this.tray) {
      this.tray.destroy()
      this.tray = null
      console.log('System tray destroyed')
    }
  }

  public showBalloon(title: string, content: string): void {
    if (this.tray && process.platform === 'win32') {
      this.tray.displayBalloon({
        iconType: 'info',
        title,
        content
      })
    }
  }

  private createFallbackIcon(): Electron.NativeImage {
    // Create a simple fallback icon using SVG
    const svgIcon = `
      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <rect width="16" height="16" fill="#0078d4"/>
        <text x="8" y="12" text-anchor="middle" fill="white" font-size="10" font-family="Arial">OD</text>
      </svg>
    `
    
    return nativeImage.createFromDataURL(`data:image/svg+xml;base64,${Buffer.from(svgIcon).toString('base64')}`)
  }

  private updateContextMenu(): void {
    if (!this.tray) return

    const isVisible = this.mainWindow?.isVisible() ?? false
    
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: isVisible ? '隐藏窗口' : '显示窗口',
        click: () => this.toggleWindowVisibility()
      },
      {
        type: 'separator'
      },
      {
        label: '退出程序',
        click: () => {
          if (this.mainWindow) {
            this.mainWindow.destroy()
          }
          process.exit(0)
        }
      }
    ]
    
    const contextMenu = Menu.buildFromTemplate(template)
    this.tray.setContextMenu(contextMenu)
  }

  private toggleWindowVisibility(): void {
    if (!this.mainWindow) return

    if (this.mainWindow.isVisible()) {
      this.mainWindow.hide()
    } else {
      this.mainWindow.show()
      this.mainWindow.focus()
    }
  }
} 