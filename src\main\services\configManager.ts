import { app } from 'electron'
import * as path from 'path'
import * as fs from 'fs'
import { DEFAULT_APP_CONFIG } from '../config'

export interface UserConfig {
  auth: {
    clientId: string
    clientSecret: string
    authority: string
    redirectUri: string
  }
  app?: {
    theme?: string
    language?: string
    autoStart?: boolean
  }
}

export class ConfigManager {
  private configPath: string
  private config: UserConfig | null = null

  constructor() {
    // 获取用户数据目录
    // Windows: C:\Users\<USER>\AppData\Roaming\od-commander
    // macOS: ~/Library/Application Support/od-commander  
    // Linux: ~/.config/od-commander
    const userDataPath = app.getPath('userData')
    this.configPath = path.join(userDataPath, 'config.json')
    
    console.log('📁 Config file path:', this.configPath)
  }

  /**
   * 加载用户配置
   */
  async loadConfig(): Promise<UserConfig> {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8')
        this.config = JSON.parse(configData)
        console.log('✅ User config loaded successfully')
        
        // 验证配置完整性
        if (!this.config?.auth?.clientId || !this.config?.auth?.clientSecret) {
          console.log('⚠️ Config incomplete, will prompt user for setup')
          return this.getDefaultConfig()
        }
        
        return this.config
      } else {
        console.log('📝 No config file found, using defaults')
        return this.getDefaultConfig()
      }
    } catch (error) {
      console.error('❌ Failed to load config:', error)
      return this.getDefaultConfig()
    }
  }

  /**
   * 保存用户配置
   */
  async saveConfig(config: UserConfig): Promise<void> {
    try {
      // 确保目录存在
      const configDir = path.dirname(this.configPath)
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true })
      }

      // 保存配置
      const configData = JSON.stringify(config, null, 2)
      fs.writeFileSync(this.configPath, configData, 'utf8')
      
      this.config = config
      console.log('💾 User config saved successfully')
    } catch (error) {
      console.error('❌ Failed to save config:', error)
      throw error
    }
  }

  /**
   * 检查配置是否完整
   */
  isConfigComplete(): boolean {
    return !!(this.config?.auth?.clientId && this.config?.auth?.clientSecret)
  }

  /**
   * 获取当前配置
   */
  getConfig(): UserConfig {
    return this.config || this.getDefaultConfig()
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): UserConfig {
    return {
      auth: {
        clientId: '',
        clientSecret: '',
        authority: DEFAULT_APP_CONFIG.auth.authority,
        redirectUri: DEFAULT_APP_CONFIG.auth.redirectUri
      },
      app: {
        theme: 'system',
        language: 'zh-CN',
        autoStart: false
      }
    }
  }

  /**
   * 更新认证配置
   */
  async updateAuthConfig(authConfig: {
    clientId: string
    clientSecret: string
  }): Promise<void> {
    const currentConfig = this.getConfig()
    currentConfig.auth.clientId = authConfig.clientId.trim()
    currentConfig.auth.clientSecret = authConfig.clientSecret.trim()
    
    await this.saveConfig(currentConfig)
  }

  /**
   * 清除认证配置（用于退出登录）
   */
  async clearAuthConfig(): Promise<void> {
    const currentConfig = this.getConfig()
    currentConfig.auth.clientId = ''
    currentConfig.auth.clientSecret = ''
    
    await this.saveConfig(currentConfig)
  }

  /**
   * 获取配置文件路径（用于用户手动编辑）
   */
  getConfigPath(): string {
    return this.configPath
  }

  /**
   * 打开配置文件所在目录
   */
  openConfigDirectory(): void {
    const { shell } = require('electron')
    const configDir = path.dirname(this.configPath)
    shell.openPath(configDir)
  }

  /**
   * 验证Azure配置格式
   */
  validateAzureConfig(clientId: string, clientSecret: string): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // 验证Client ID格式 (GUID)
    const clientIdPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!clientId || !clientIdPattern.test(clientId)) {
      errors.push('客户端ID格式不正确，应为GUID格式 (例: 12345678-1234-1234-1234-123456789012)')
    }

    // 验证Client Secret格式
    if (!clientSecret || clientSecret.length < 10) {
      errors.push('客户端密码不能为空且长度应大于10个字符')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// 全局配置管理器实例
export const configManager = new ConfigManager() 