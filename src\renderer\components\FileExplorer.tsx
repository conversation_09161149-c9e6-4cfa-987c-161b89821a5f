import React, { useState, useEffect } from 'react'
import { useFileStore } from '../store/fileStore'
import { 
  Folder, 
  FileText, 
  FileImage, 
  FileVideo, 
  FileAudio, 
  Archive, 
  File,
  RefreshCw,
  Loader2,
  ArrowLeft,
  ChevronUp,
  ChevronDown
} from 'lucide-react'

interface DriveItem {
  id: string
  name: string
  size?: number
  lastModifiedDateTime: string
  folder?: { childCount: number }
  file?: { mimeType: string }
  webUrl: string
}

const FileExplorer: React.FC = () => {
  const { 
    currentPath, 
    files, 
    selectedFiles, 
    isLoading, 
    error,
    loadFiles, 
    navigateToFolder, 
    navigateToRoot,
    navigateToPath,
    navigateUp, 
    toggleFileSelection,
    clearSelection 
  } = useFileStore()

  const [sortBy, setSortBy] = useState<'name' | 'size' | 'modified'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  useEffect(() => {
    loadFiles()
  }, [currentPath])

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '-'
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getFileIcon = (item: DriveItem): React.ReactNode => {
    if (item.folder) return <Folder size={16} style={{ color: 'var(--text-accent)' }} />
    
    const ext = item.name.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'pdf':
      case 'doc':
      case 'docx':
      case 'txt':
        return <FileText size={16} style={{ color: 'var(--text-secondary)' }} />
      case 'xls':
      case 'xlsx':
      case 'csv':
        return <FileText size={16} style={{ color: 'var(--text-success)' }} />
      case 'ppt':
      case 'pptx':
        return <FileText size={16} style={{ color: 'var(--text-accent)' }} />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
        return <FileImage size={16} style={{ color: 'var(--text-accent)' }} />
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'mkv':
      case 'wmv':
        return <FileVideo size={16} style={{ color: 'var(--text-danger)' }} />
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return <FileAudio size={16} style={{ color: 'var(--text-accent)' }} />
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return <Archive size={16} style={{ color: 'var(--text-secondary)' }} />
      default:
        return <File size={16} style={{ color: 'var(--text-secondary)' }} />
    }
  }

  const sortedFiles = [...files].sort((a, b) => {
    // 文件夹优先
    if (a.folder && !b.folder) return -1
    if (!a.folder && b.folder) return 1

    let comparison = 0
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name)
        break
      case 'size':
        comparison = (a.size || 0) - (b.size || 0)
        break
      case 'modified':
        comparison = new Date(a.lastModifiedDateTime).getTime() - new Date(b.lastModifiedDateTime).getTime()
        break
    }

    return sortOrder === 'asc' ? comparison : -comparison
  })

  const handleSort = (column: 'name' | 'size' | 'modified') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  const handleItemClick = (item: DriveItem) => {
    toggleFileSelection(item.id)
  }

  const handleItemDoubleClick = (item: DriveItem) => {
    if (item.folder) {
      navigateToFolder(item.id, item.name)
    }
  }

  const handleItemSelect = (e: React.MouseEvent, item: DriveItem) => {
    e.stopPropagation()
    toggleFileSelection(item.id)
  }

  const pathParts = currentPath.split('/').filter(Boolean)

  const handleNavigateToPath = (index: number) => {
    if (index === -1) {
      // 导航到根目录
      navigateToRoot()
    } else {
      // 导航到指定路径
      const newPath = pathParts.slice(0, index + 1).join('/')
      navigateToPath(newPath)
    }
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 简约工具栏 */}
      <div className="modern-toolbar">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: '16px' }}>
          {/* 路径导航 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flex: 1, minWidth: 0 }}>
            <button
              onClick={navigateUp}
              disabled={!currentPath}
              className="modern-button secondary"
              style={{ 
                padding: '6px 12px',
                fontSize: '13px',
                minWidth: 'auto',
                flexShrink: 0
              }}
            >
              <ArrowLeft size={14} style={{ marginRight: '4px' }} />
              返回
            </button>
            
            <div className="breadcrumb">
              <span 
                className="breadcrumb-item"
                onClick={() => handleNavigateToPath(-1)}
              >
                根目录
              </span>
              {pathParts.map((part, index) => (
                <React.Fragment key={index}>
                  <span className="breadcrumb-separator">/</span>
                  <span 
                    className={`breadcrumb-item ${index === pathParts.length - 1 ? 'current' : ''}`}
                    onClick={() => index < pathParts.length - 1 ? handleNavigateToPath(index) : undefined}
                  >
                    {part}
                  </span>
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* 操作按钮 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flexShrink: 0 }}>
            {selectedFiles.length > 0 && (
              <>
                <span style={{ 
                  fontSize: '13px', 
                  color: 'var(--text-secondary)',
                  padding: '6px 12px',
                  background: 'var(--bg-tertiary)',
                  borderRadius: '6px',
                  whiteSpace: 'nowrap'
                }}>
                  已选择 {selectedFiles.length} 项
                </span>
                <button
                  onClick={clearSelection}
                  className="modern-button secondary"
                  style={{ fontSize: '13px', padding: '6px 12px' }}
                >
                  取消选择
                </button>
              </>
            )}
            
            <button
              onClick={loadFiles}
              disabled={isLoading}
              className="modern-button secondary"
              style={{ fontSize: '13px', padding: '6px 12px' }}
            >
              {isLoading ? (
                <>
                  <Loader2 size={14} className="animate-spin" style={{ marginRight: '4px' }} />
                  刷新中...
                </>
              ) : (
                <>
                  <RefreshCw size={14} style={{ marginRight: '4px' }} />
                  刷新
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div style={{
          padding: '12px 16px',
          marginBottom: '16px',
          fontSize: '14px',
          fontWeight: '500'
        }} className="message-error">
          {error}
        </div>
      )}

      {/* 文件列表 */}
      <div className="modern-table" style={{ flex: 1 }}>
        {/* 表头 */}
        <div style={{ display: 'flex', background: 'var(--bg-secondary)' }}>
          <div 
            className="modern-table-header"
            style={{ 
              flex: 1, 
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              minWidth: 200
            }}
            onClick={() => handleSort('name')}
          >
            名称
            {sortBy === 'name' && (
              sortOrder === 'asc' ? <ChevronUp size={12} /> : <ChevronDown size={12} />
            )}
          </div>
          <div 
            className="modern-table-header"
            style={{ 
              width: '100px', 
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              flexShrink: 0
            }}
            onClick={() => handleSort('size')}
          >
            大小
            {sortBy === 'size' && (
              sortOrder === 'asc' ? <ChevronUp size={12} /> : <ChevronDown size={12} />
            )}
          </div>
          <div 
            className="modern-table-header"
            style={{ 
              width: '140px', 
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              flexShrink: 0
            }}
            onClick={() => handleSort('modified')}
          >
            修改时间
            {sortBy === 'modified' && (
              sortOrder === 'asc' ? <ChevronUp size={12} /> : <ChevronDown size={12} />
            )}
          </div>
          <div className="modern-table-header" style={{ width: '50px', flexShrink: 0 }}>
            选择
          </div>
        </div>

        {/* 文件列表内容 */}
        <div className="modern-table-body scrollable">
          {isLoading ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '200px',
              flexDirection: 'column',
              gap: '12px',
              color: 'var(--text-secondary)'
            }}>
              <Loader2 size={20} className="animate-spin" />
              <span style={{ fontSize: '14px' }}>加载中...</span>
            </div>
          ) : sortedFiles.length === 0 ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '200px',
              flexDirection: 'column',
              gap: '12px',
              color: 'var(--text-secondary)'
            }}>
              <Folder size={48} style={{ color: 'var(--text-secondary)' }} />
              <span style={{ fontSize: '14px' }}>此文件夹为空</span>
              <span style={{ fontSize: '12px', color: 'var(--text-tertiary)' }}>
                您可以拖拽文件到这里或使用左侧的上传功能
              </span>
            </div>
          ) : (
            sortedFiles.map((item) => (
              <div
                key={item.id}
                className={`modern-table-row ${selectedFiles.includes(item.id) ? 'selected' : ''}`}
                onClick={() => handleItemClick(item)}
                onDoubleClick={() => handleItemDoubleClick(item)}
                style={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  cursor: item.folder ? 'pointer' : 'default'
                }}
              >
                <div style={{ 
                  flex: 1, 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: '10px',
                  minWidth: 0
                }}>
                  <span style={{ flexShrink: 0, display: 'flex', alignItems: 'center' }}>
                    {getFileIcon(item)}
                  </span>
                  <span className="file-name" style={{ 
                    fontWeight: '500',
                    color: 'var(--text-primary)'
                  }}>
                    {item.name}
                  </span>
                </div>
                
                <div style={{ 
                  width: '100px', 
                  fontSize: '13px', 
                  color: 'var(--text-secondary)',
                  textAlign: 'right',
                  flexShrink: 0
                }}>
                  {item.folder ? '-' : formatFileSize(item.size)}
                </div>
                
                <div style={{ 
                  width: '140px', 
                  fontSize: '13px', 
                  color: 'var(--text-secondary)',
                  textAlign: 'right',
                  flexShrink: 0
                }}>
                  {formatDate(item.lastModifiedDateTime)}
                </div>
                
                <div style={{ 
                  width: '50px', 
                  display: 'flex', 
                  justifyContent: 'center',
                  flexShrink: 0
                }}>
                  <input
                    type="checkbox"
                    className="modern-checkbox"
                    checked={selectedFiles.includes(item.id)}
                    onChange={(e) => handleItemSelect(e, item)}
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* 简约状态栏 */}
      <div className="modern-status-bar">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>
            {files.length} 项 
            {files.filter(f => f.folder).length > 0 && 
              ` (${files.filter(f => f.folder).length} 个文件夹)`
            }
          </span>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {selectedFiles.length > 0 && (
              <span>已选择 {selectedFiles.length} 项</span>
            )}
            <span style={{ fontSize: '11px', color: 'var(--text-tertiary)' }}>
              双击文件夹打开 • 单击选择文件
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FileExplorer 