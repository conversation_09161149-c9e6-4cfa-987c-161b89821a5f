import React, { useState } from 'react'
import { useFileStore } from '../store/fileStore'
import { 
  Upload, 
  FolderPlus, 
  Download, 
  Trash2, 
  RefreshCw, 
  Loader2,
  Lightbulb,
  Clipboard
} from 'lucide-react'

const FileOperations: React.FC = () => {
  const { 
    selectedFiles, 
    currentPath,
    uploadFile, 
    downloadFile, 
    deleteFiles, 
    createFolder,
    loadFiles 
  } = useFileStore()

  const [isUploading, setIsUploading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isCreatingFolder, setIsCreatingFolder] = useState(false)
  const [newFolderName, setNewFolderName] = useState('')
  const [showCreateFolder, setShowCreateFolder] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text })
    setTimeout(() => setMessage(null), 3000)
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setIsUploading(true)
    try {
      for (const file of Array.from(files)) {
        await uploadFile(file)
      }
      showMessage('success', `成功上传 ${files.length} 个文件`)
      await loadFiles()
    } catch (error) {
      console.error('上传失败:', error)
      showMessage('error', '上传失败，请重试')
    } finally {
      setIsUploading(false)
      // 清空文件输入
      event.target.value = ''
    }
  }

  const handleDownload = async () => {
    if (selectedFiles.length === 0) {
      showMessage('error', '请先选择要下载的文件')
      return
    }

    try {
      for (const fileId of selectedFiles) {
        await downloadFile(fileId)
      }
      showMessage('success', `成功下载 ${selectedFiles.length} 个文件`)
    } catch (error) {
      console.error('下载失败:', error)
      showMessage('error', '下载失败，请重试')
    }
  }

  const handleDelete = async () => {
    if (selectedFiles.length === 0) {
      showMessage('error', '请先选择要删除的文件')
      return
    }

    if (!confirm(`确定要删除选中的 ${selectedFiles.length} 个项目吗？此操作不可撤销。`)) {
      return
    }

    setIsDeleting(true)
    try {
      await deleteFiles(selectedFiles)
      showMessage('success', `成功删除 ${selectedFiles.length} 个项目`)
      await loadFiles()
    } catch (error) {
      console.error('删除失败:', error)
      showMessage('error', '删除失败，请重试')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      showMessage('error', '请输入文件夹名称')
      return
    }

    setIsCreatingFolder(true)
    try {
      await createFolder(newFolderName.trim())
      showMessage('success', `成功创建文件夹 "${newFolderName}"`)
      setNewFolderName('')
      setShowCreateFolder(false)
      await loadFiles()
    } catch (error) {
      console.error('创建文件夹失败:', error)
      showMessage('error', '创建文件夹失败，请重试')
    } finally {
      setIsCreatingFolder(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCreateFolder()
    } else if (e.key === 'Escape') {
      setShowCreateFolder(false)
      setNewFolderName('')
    }
  }

  return (
    <div className="modern-sidebar">
      <div className="modern-sidebar-content scrollable">
        {/* 标题 */}
        <div style={{ marginBottom: '20px' }}>
          <h3 style={{
            fontSize: '16px',
            fontWeight: '600',
            color: 'var(--text-primary)',
            margin: '0 0 8px 0'
          }}>
            文件操作
          </h3>
          <p style={{
            fontSize: '13px',
            color: 'var(--text-secondary)',
            margin: 0,
            lineHeight: '1.4'
          }}>
            管理您的 OneDrive 文件
          </p>
        </div>

        {/* 消息提示 */}
        {message && (
          <div style={{
            padding: '10px 12px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            marginBottom: '16px'
          }} className={message.type === 'success' ? 'message-success' : 'message-error'}>
            {message.text}
          </div>
        )}

        {/* 快速操作区域 */}
        <div style={{ marginBottom: '24px' }}>
          <h4 style={{
            fontSize: '14px',
            fontWeight: '600',
            color: 'var(--text-primary)',
            margin: '0 0 12px 0'
          }}>
            快速操作
          </h4>

          {/* 上传文件 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ position: 'relative' }}>
              <input
                type="file"
                multiple
                onChange={handleFileUpload}
                disabled={isUploading}
                style={{
                  position: 'absolute',
                  opacity: 0,
                  width: '100%',
                  height: '100%',
                  cursor: isUploading ? 'not-allowed' : 'pointer'
                }}
              />
              <button
                className="modern-button"
                disabled={isUploading}
                style={{
                  width: '100%',
                  justifyContent: 'center',
                  pointerEvents: 'none'
                }}
              >
                {isUploading ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    上传中...
                  </>
                ) : (
                  <>
                    <Upload size={16} />
                    上传文件
                  </>
                )}
              </button>
            </div>
          </div>

          {/* 创建文件夹 */}
          <div style={{ marginBottom: '12px' }}>
            {!showCreateFolder ? (
              <button
                onClick={() => setShowCreateFolder(true)}
                className="modern-button secondary"
                style={{ width: '100%', justifyContent: 'center' }}
              >
                <FolderPlus size={16} />
                新建文件夹
              </button>
            ) : (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <input
                  type="text"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  placeholder="输入文件夹名称"
                  className="modern-input"
                  style={{ fontSize: '14px' }}
                  onKeyDown={handleKeyPress}
                  autoFocus
                />
                <div style={{ display: 'flex', gap: '8px' }}>
                  <button
                    onClick={handleCreateFolder}
                    disabled={isCreatingFolder || !newFolderName.trim()}
                    className="modern-button success"
                    style={{ flex: 1, fontSize: '13px' }}
                  >
                    {isCreatingFolder ? (
                      <span className="modern-loading"></span>
                    ) : (
                      '创建'
                    )}
                  </button>
                  <button
                    onClick={() => {
                      setShowCreateFolder(false)
                      setNewFolderName('')
                    }}
                    className="modern-button secondary"
                    style={{ flex: 1, fontSize: '13px' }}
                  >
                    取消
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 分割线 */}
        <div style={{
          height: '1px',
          background: 'var(--border-primary)',
          margin: '20px 0'
        }}></div>

        {/* 选中文件操作 */}
        <div style={{ marginBottom: '24px' }}>
          <h4 style={{
            fontSize: '14px',
            fontWeight: '600',
            color: 'var(--text-primary)',
            margin: '0 0 12px 0'
          }}>
            选中文件操作
          </h4>
          
          {selectedFiles.length === 0 ? (
            <div style={{
              padding: '16px',
              background: 'var(--bg-secondary)',
              borderRadius: '8px',
              textAlign: 'center',
              color: 'var(--text-secondary)',
              fontSize: '13px'
            }}>
              <Clipboard size={24} style={{ marginBottom: '8px', color: 'var(--text-secondary)' }} />
              <div>请先选择文件或文件夹</div>
              <div style={{ fontSize: '12px', marginTop: '4px', color: 'var(--text-tertiary)' }}>
                在右侧文件列表中点击选择
              </div>
            </div>
          ) : (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <div className="modern-info-banner accent">
                <div className="banner-title">
                  已选择 {selectedFiles.length} 项
                </div>
                <div className="banner-text">
                  可以进行下载或删除操作
                </div>
              </div>
              
              <button
                onClick={handleDownload}
                className="modern-button"
                style={{ width: '100%', justifyContent: 'center', fontSize: '13px' }}
              >
                <Download size={16} />
                下载选中项
              </button>
              
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="modern-button danger"
                style={{ width: '100%', justifyContent: 'center', fontSize: '13px' }}
              >
                {isDeleting ? (
                  <>
                    <span className="modern-loading"></span>
                    删除中...
                  </>
                ) : (
                  <>
                    <Trash2 size={16} />
                  删除选中项
                  </>
                )}
              </button>
            </div>
          )}
        </div>

        {/* 当前路径信息 */}
        <div style={{
          padding: '12px',
          background: 'var(--bg-secondary)',
          borderRadius: '8px',
          border: '1px solid var(--border-primary)'
        }}>
          <div style={{
            fontSize: '12px',
            color: 'var(--text-secondary)',
            marginBottom: '4px',
            fontWeight: '600'
          }}>
            当前位置
          </div>
          <div style={{
            fontSize: '13px',
            color: 'var(--text-primary)',
            fontWeight: '500',
            wordBreak: 'break-all',
            lineHeight: '1.3'
          }}>
            {currentPath || '根目录'}
          </div>
        </div>

        {/* 使用提示 */}
        <div className="modern-info-banner" style={{ marginTop: '20px' }}>
          <div className="banner-title">
            <Lightbulb size={14} style={{ display: 'inline', marginRight: '4px' }} />
            使用提示
          </div>
          <ul style={{
            fontSize: '11px',
            color: 'var(--text-secondary)',
            margin: '6px 0 0 0',
            paddingLeft: '16px',
            lineHeight: '1.4'
          }}>
            <li>双击文件夹可以打开</li>
            <li>单击文件可以选择</li>
            <li>支持多选文件操作</li>
            <li>可以拖拽文件上传</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default FileOperations 