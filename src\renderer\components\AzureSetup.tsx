import React, { useState, useEffect } from 'react'
import './AzureSetup.css'

interface AzureConfig {
  clientId: string
  hasClientSecret: boolean
  isComplete: boolean
}

interface AzureSetupProps {
  onConfigComplete: () => void
  onCancel?: () => void
}

const AzureSetup: React.FC<AzureSetupProps> = ({ onConfigComplete, onCancel }) => {
  const [config, setConfig] = useState<AzureConfig | null>(null)
  const [clientId, setClientId] = useState('')
  const [clientSecret, setClientSecret] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showSetupGuide, setShowSetupGuide] = useState(false)
  const [configPath, setConfigPath] = useState('')

  useEffect(() => {
    loadConfig()
    loadConfigPath()
  }, [])

  const loadConfig = async () => {
    try {
      const result = await window.electron.ipcRenderer.invoke('azure:get-config')
      if (result.success) {
        setConfig(result.config)
        setClientId(result.config.clientId || '')
      }
    } catch (error) {
      setError('加载配置失败')
    }
  }

  const loadConfigPath = async () => {
    try {
      const path = await window.electron.ipcRenderer.invoke('azure:get-config-path')
      setConfigPath(path)
    } catch (error) {
      console.error('Failed to get config path:', error)
    }
  }

  const handleSaveConfig = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!clientId.trim() || !clientSecret.trim()) {
      setError('请填写所有必填字段')
      return
    }

    setLoading(true)
    setError('')

    try {
      const result = await window.electron.ipcRenderer.invoke('azure:save-config', {
        clientId: clientId.trim(),
        clientSecret: clientSecret.trim()
      })

      if (result.success) {
        onConfigComplete()
      } else {
        setError(result.error || '保存配置失败')
      }
    } catch (error) {
      setError('保存配置时发生错误')
    } finally {
      setLoading(false)
    }
  }

  const openConfigDirectory = async () => {
    try {
      await window.electron.ipcRenderer.invoke('azure:open-config-dir')
    } catch (error) {
      setError('打开配置目录失败')
    }
  }

  const openAzurePortal = () => {
    window.electron.shell.openExternal('https://portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationsListBlade')
  }

  if (config?.isComplete && !showSetupGuide) {
    return (
      <div className="azure-setup-container">
        <div className="azure-setup-card">
          <div className="setup-header">
            <h2>🔧 Azure 配置</h2>
            <p className="setup-subtitle">您的Azure应用注册已配置完成</p>
          </div>

          <div className="config-info">
            <div className="info-item">
              <label>客户端ID:</label>
              <span className="config-value">{config.clientId.substring(0, 8)}...{config.clientId.substring(config.clientId.length - 8)}</span>
            </div>
            <div className="info-item">
              <label>客户端密码:</label>
              <span className="config-value">已配置 ✅</span>
            </div>
          </div>

          <div className="button-group">
            <button 
              type="button" 
              className="button secondary"
              onClick={() => setShowSetupGuide(true)}
            >
              重新配置
            </button>
            <button 
              type="button" 
              className="button secondary"
              onClick={openConfigDirectory}
            >
              打开配置目录
            </button>
            <button 
              type="button" 
              className="button primary"
              onClick={onConfigComplete}
            >
              继续
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="azure-setup-container">
      <div className="azure-setup-card">
        <div className="setup-header">
          <h2>🔧 Azure 应用注册配置</h2>
          <p className="setup-subtitle">
            OneDrive Commander 需要您创建自己的 Azure 应用注册以获得长期授权
          </p>
        </div>

        <div className="setup-benefits">
          <h3>✨ 配置后的优势：</h3>
          <ul>
            <li>✅ 真正的90天refresh token有效期</li>
            <li>✅ 安全的Authorization Code Flow with PKCE</li>
            <li>✅ 自动后台token刷新</li>
            <li>✅ 企业级安全性和合规性</li>
            <li>✅ 无需频繁重新登录</li>
          </ul>
        </div>

        <div className="setup-guide">
          <h3>📋 快速设置指南：</h3>
          <ol>
            <li>
              点击下方按钮打开 Azure Portal
              <button 
                type="button" 
                className="link-button"
                onClick={openAzurePortal}
              >
                🔗 打开 Azure Portal
              </button>
            </li>
            <li>点击 "新注册" 创建应用</li>
            <li>配置应用名称和重定向URI: <code>http://localhost:3000/auth/callback</code></li>
            <li>在 "证书和密码" 中创建客户端密码</li>
            <li>在 "API权限" 中添加所需权限</li>
            <li>将客户端ID和密码填入下方表单</li>
          </ol>
          
          <div className="guide-link">
            <a 
              href="#" 
              onClick={(e) => {
                e.preventDefault()
                window.electron.shell.openExternal(`file://${process.cwd()}/README_AZURE_SETUP.md`)
              }}
            >
              📖 查看详细设置指南
            </a>
          </div>
        </div>

        <form onSubmit={handleSaveConfig} className="config-form">
          <div className="form-group">
            <label htmlFor="clientId">
              客户端ID <span className="required">*</span>
            </label>
            <input
              type="text"
              id="clientId"
              value={clientId}
              onChange={(e) => setClientId(e.target.value)}
              placeholder="12345678-1234-1234-1234-123456789012"
              className="form-input"
              required
            />
            <small className="form-help">
              从Azure Portal应用主页复制"应用程序(客户端) ID"
            </small>
          </div>

          <div className="form-group">
            <label htmlFor="clientSecret">
              客户端密码 <span className="required">*</span>
            </label>
            <input
              type="password"
              id="clientSecret"
              value={clientSecret}
              onChange={(e) => setClientSecret(e.target.value)}
              placeholder="输入客户端密码"
              className="form-input"
              required
            />
            <small className="form-help">
              从"证书和密码"页面生成的密码值（只显示一次！）
            </small>
          </div>

          {error && (
            <div className="error-message">
              ❌ {error}
            </div>
          )}

          <div className="form-actions">
            {onCancel && (
              <button 
                type="button" 
                className="button secondary"
                onClick={onCancel}
                disabled={loading}
              >
                取消
              </button>
            )}
            
            <button 
              type="button" 
              className="button secondary"
              onClick={openConfigDirectory}
              disabled={loading}
            >
              打开配置目录
            </button>
            
            <button 
              type="submit" 
              className="button primary"
              disabled={loading}
            >
              {loading ? '保存中...' : '保存配置'}
            </button>
          </div>
        </form>

        <div className="config-file-info">
          <small>
            **Windows**: <code>C:\Users\<USER>\AppData\Roaming\od-commander\config.json</code><br/>
            **实际路径**: <code>{configPath}</code><br/>
            您也可以直接编辑此文件，然后重启应用
          </small>
        </div>
      </div>
    </div>
  )
}

export default AzureSetup 