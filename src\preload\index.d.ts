export interface IElectronAPI {
  ping: () => Promise<string>
  
  auth: {
    login: () => Promise<{
      success: boolean
      accessToken?: string
      userInfo?: {
        id: string
        displayName: string
        mail: string
        userPrincipalName: string
      }
      error?: string
    }>
    logout: () => Promise<{ success: boolean; error?: string }>
  }
  
  files: {
    list: (path?: string) => Promise<{
      success: boolean
      items?: Array<{
        id: string
        name: string
        size?: number
        lastModifiedDateTime: string
        folder?: any
        file?: any
        webUrl: string
        downloadUrl?: string
      }>
      error?: string
    }>
    createFolder: (name: string, parentPath?: string) => Promise<{
      success: boolean
      folder?: any
      error?: string
    }>
    upload: (filePath: string, fileName: string, parentPath?: string) => Promise<{
      success: boolean
      file?: any
      error?: string
    }>
    download: (itemId: string, localPath: string) => Promise<{
      success: boolean
      error?: string
    }>
    delete: (itemId: string) => Promise<{
      success: boolean
      error?: string
    }>
    search: (query: string) => Promise<{
      success: boolean
      items?: Array<any>
      error?: string
    }>
  }
}

declare global {
  interface Window {
    electronAPI: IElectronAPI
  }
} 